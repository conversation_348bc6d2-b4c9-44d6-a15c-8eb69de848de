<?php
/**
 * Application Configuration File
 * 
 * Contains all application-wide configuration settings
 */

// Application settings
define('APP_NAME', '<PERSON><PERSON>ống <PERSON>n <PERSON>');
define('APP_VERSION', '1.0.0');
define('BASE_URL', 'http://do-an.test:8189');
define('DEFAULT_LANGUAGE', 'vi');
define('DEFAULT_LOCALE', 'vi_VN');

// Security settings
define('CSRF_TOKEN_NAME', 'csrf_token');
define('SESSION_TIMEOUT', 3600); // 1 hour in seconds

// File upload settings
define('UPLOAD_PATH', 'assets/images/uploads/');
define('MAX_FILE_SIZE', 5242880); // 5MB in bytes
define('ALLOWED_EXTENSIONS', ['jpg', 'jpeg', 'png', 'gif']);

// Pagination settings
define('ITEMS_PER_PAGE', 10);

// Date and time settings
define('DEFAULT_TIMEZONE', 'Asia/Ho_Chi_Minh');
date_default_timezone_set(DEFAULT_TIMEZONE);

// Localization settings
setlocale(LC_TIME, 'vi_VN.UTF-8', 'vi_VN', 'vietnamese');
setlocale(LC_MONETARY, 'vi_VN.UTF-8', 'vi_VN', 'vietnamese');
setlocale(LC_NUMERIC, 'vi_VN.UTF-8', 'vi_VN', 'vietnamese');

// Include language file
require_once __DIR__ . '/../includes/lang_vi.php';

// Error reporting (set to 0 in production)
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Session configuration
ini_set('session.cookie_httponly', 1);
ini_set('session.use_only_cookies', 1);
ini_set('session.cookie_secure', 0); // Set to 1 if using HTTPS

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// CSRF Token generation
function generateCSRFToken() {
    if (!isset($_SESSION[CSRF_TOKEN_NAME])) {
        $_SESSION[CSRF_TOKEN_NAME] = bin2hex(random_bytes(32));
    }
    return $_SESSION[CSRF_TOKEN_NAME];
}

// CSRF Token validation
function validateCSRFToken($token) {
    return isset($_SESSION[CSRF_TOKEN_NAME]) && hash_equals($_SESSION[CSRF_TOKEN_NAME], $token);
}

// Auto-logout on session timeout
function checkSessionTimeout() {
    if (isset($_SESSION['last_activity'])) {
        if (time() - $_SESSION['last_activity'] > SESSION_TIMEOUT) {
            session_unset();
            session_destroy();
            header('Location: ' . BASE_URL . '/views/auth/login.php?timeout=1');
            exit();
        }
    }
    $_SESSION['last_activity'] = time();
}

// Check if user is logged in
function isLoggedIn() {
    return isset($_SESSION['user_id']) && isset($_SESSION['username']);
}

// Check if user has specific role
function hasRole($role) {
    return isset($_SESSION['role']) && $_SESSION['role'] === $role;
}

// Redirect if not logged in
function requireLogin() {
    if (!isLoggedIn()) {
        header('Location: ' . BASE_URL . '/views/auth/login.php');
        exit();
    }
    checkSessionTimeout();
}

// Redirect if not admin
function requireAdmin() {
    requireLogin();
    if (!hasRole('admin')) {
        header('Location: ' . BASE_URL . '/views/auth/unauthorized.php');
        exit();
    }
}

// Sanitize input data
function sanitizeInput($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
    return $data;
}

// Format currency for Vietnamese Dong
function formatCurrency($amount) {
    // Handle null or empty values
    if ($amount === null || $amount === '') {
        return '0 ₫';
    }

    // Convert to float to ensure numeric value
    $amount = (float)$amount;

    // Format with Vietnamese number conventions
    // Use period as thousands separator, no decimal places for VND
    return number_format($amount, 0, ',', '.') . ' ₫';
}

// Format date for Vietnamese locale
function formatDate($date, $format = 'd/m/Y H:i') {
    if (empty($date) || $date === '0000-00-00 00:00:00') {
        return '';
    }

    $timestamp = is_numeric($date) ? $date : strtotime($date);
    if ($timestamp === false) {
        return $date; // Return original if parsing fails
    }

    return date($format, $timestamp);
}

// Format Vietnamese date with day names
function formatVietnameseDate($date, $includeTime = true) {
    if (empty($date) || $date === '0000-00-00 00:00:00') {
        return '';
    }

    $timestamp = is_numeric($date) ? $date : strtotime($date);
    if ($timestamp === false) {
        return $date;
    }

    $dayNames = [
        'Monday' => 'Thứ Hai',
        'Tuesday' => 'Thứ Ba',
        'Wednesday' => 'Thứ Tư',
        'Thursday' => 'Thứ Năm',
        'Friday' => 'Thứ Sáu',
        'Saturday' => 'Thứ Bảy',
        'Sunday' => 'Chủ Nhật'
    ];

    $dayName = date('l', $timestamp);
    $vietnameseDayName = $dayNames[$dayName] ?? $dayName;

    if ($includeTime) {
        return $vietnameseDayName . ', ' . date('d/m/Y H:i', $timestamp);
    } else {
        return $vietnameseDayName . ', ' . date('d/m/Y', $timestamp);
    }
}

// Generate random string
function generateRandomString($length = 10) {
    return substr(str_shuffle(str_repeat($x='0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ', ceil($length/strlen($x)) )),1,$length);
}

// Log activity
function logActivity($message, $user_id = null) {
    $log_file = 'logs/activity.log';
    $timestamp = date('Y-m-d H:i:s');
    $user_info = $user_id ? "User ID: $user_id" : "Guest";
    $log_message = "[$timestamp] $user_info - $message" . PHP_EOL;
    
    if (!file_exists('logs')) {
        mkdir('logs', 0755, true);
    }
    
    file_put_contents($log_file, $log_message, FILE_APPEND | LOCK_EX);
}
?>
