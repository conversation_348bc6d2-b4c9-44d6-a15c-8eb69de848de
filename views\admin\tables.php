<?php
/**
 * Tables Management Page
 * 
 * Admin interface for managing restaurant tables including CRUD operations,
 * status management, and table statistics.
 */

require_once '../../config/config.php';
require_once '../../config/database.php';
require_once '../../includes/functions.php';
require_once '../../models/Table.php';

// Check admin access
requireAdmin();

// Initialize table model
$tableModel = new Table();

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
        setFlashMessage('error', __('invalid_csrf_token', 'Token bảo mật không hợp lệ. Vui lòng thử lại.'));
    } else {
        $action = $_POST['action'] ?? '';
        
        switch ($action) {
            case 'create':
                $tableData = [
                    'table_number' => sanitizeInput($_POST['table_number'] ?? ''),
                    'capacity' => (int)($_POST['capacity'] ?? 0),
                    'status' => sanitizeInput($_POST['status'] ?? 'available')
                ];
                
                if (empty($tableData['table_number']) || $tableData['capacity'] <= 0) {
                    setFlashMessage('error', __('table_required_fields', 'Số bàn và sức chứa là bắt buộc.'));
                } else {
                    $tableId = $tableModel->createTable($tableData);
                    if ($tableId) {
                        setFlashMessage('success', __('table_created_success', 'Tạo bàn thành công.'));
                        logActivity("Tạo bàn: {$tableData['table_number']}", $_SESSION['user_id']);
                    } else {
                        setFlashMessage('error', __('table_create_failed', 'Tạo bàn thất bại. Số bàn có thể đã tồn tại.'));
                    }
                }
                break;
                
            case 'update':
                $tableId = (int)($_POST['table_id'] ?? 0);
                $tableData = [
                    'table_number' => sanitizeInput($_POST['table_number'] ?? ''),
                    'capacity' => (int)($_POST['capacity'] ?? 0),
                    'status' => sanitizeInput($_POST['status'] ?? 'available')
                ];
                
                if (empty($tableData['table_number']) || $tableData['capacity'] <= 0) {
                    setFlashMessage('error', __('table_required_fields', 'Số bàn và sức chứa là bắt buộc.'));
                } else {
                    if ($tableModel->updateTable($tableId, $tableData)) {
                        setFlashMessage('success', __('table_updated_success', 'Cập nhật bàn thành công.'));
                        logActivity("Cập nhật bàn ID: $tableId", $_SESSION['user_id']);
                    } else {
                        setFlashMessage('error', __('table_update_failed', 'Cập nhật bàn thất bại. Số bàn có thể đã tồn tại.'));
                    }
                }
                break;
                
            case 'delete':
                $tableId = (int)($_POST['table_id'] ?? 0);
                if ($tableModel->deleteTable($tableId)) {
                    setFlashMessage('success', __('table_deleted_success', 'Xóa bàn thành công.'));
                    logActivity("Xóa bàn ID: $tableId", $_SESSION['user_id']);
                } else {
                    setFlashMessage('error', __('table_delete_failed', 'Xóa bàn thất bại. Bàn có thể có đơn hàng liên quan.'));
                }
                break;
        }
        
        // Redirect to prevent form resubmission
        header('Location: tables.php');
        exit();
    }
}

// Handle GET requests for editing
$editTable = null;
if (isset($_GET['edit'])) {
    $editId = (int)$_GET['edit'];
    $editTable = $tableModel->getTableById($editId);
}

// Get tables with pagination and search
$page = (int)($_GET['page'] ?? 1);
$search = sanitizeInput($_GET['search'] ?? '');
$statusFilter = sanitizeInput($_GET['status'] ?? '');

if ($search) {
    $tablesData = $tableModel->searchTables($search, $page);
} else {
    $tablesData = $tableModel->getAllTables($page, ITEMS_PER_PAGE, $statusFilter);
}

// Get table statistics
$tableStats = $tableModel->getTableStats();

$pageTitle = __('tables_management', 'Quản lý bàn') . ' - ' . APP_NAME;
?>
<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?></title>
    
    <!-- MDBootstrap CSS -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/mdb-ui-kit/6.4.2/mdb.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="../../assets/css/custom.css" rel="stylesheet">
</head>
<body>
    <!-- Sidebar -->
    <nav class="sidebar">
        <div class="p-3">
            <div class="text-center mb-4">
                <i class="fas fa-utensils fa-2x text-white mb-2"></i>
                <h5 class="text-white mb-0"><?php echo APP_NAME; ?></h5>
                <small class="text-white-50"><?php echo __('admin', 'Quản trị viên'); ?></small>
            </div>
            
            <div class="mb-3">
                <small class="text-white-50 text-uppercase"><?php echo __('dashboard_welcome', 'Chào mừng'); ?></small>
                <div class="text-white">
                    <i class="fas fa-user-shield me-2"></i>
                    <?php echo htmlspecialchars($_SESSION['full_name']); ?>
                </div>
            </div>
            
            <hr class="text-white-50">
            
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link" href="dashboard.php">
                        <i class="fas fa-tachometer-alt me-2"></i><?php echo __('dashboard', 'Bảng điều khiển'); ?>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="categories.php">
                        <i class="fas fa-tags me-2"></i><?php echo __('categories', 'Danh mục'); ?>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="food-items.php">
                        <i class="fas fa-hamburger me-2"></i><?php echo __('food_items', 'Món ăn'); ?>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link active" href="tables.php">
                        <i class="fas fa-chair me-2"></i><?php echo __('tables', 'Bàn'); ?>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="users.php">
                        <i class="fas fa-users me-2"></i><?php echo __('users', 'Người dùng'); ?>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="orders.php">
                        <i class="fas fa-receipt me-2"></i><?php echo __('orders', 'Đơn hàng'); ?>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="reports.php">
                        <i class="fas fa-chart-bar me-2"></i><?php echo __('reports', 'Báo cáo'); ?>
                    </a>
                </li>
            </ul>
            
            <hr class="text-white-50">
            
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link" href="../auth/logout.php">
                        <i class="fas fa-sign-out-alt me-2"></i><?php echo __('logout', 'Đăng xuất'); ?>
                    </a>
                </li>
            </ul>
        </div>
    </nav>
    
    <!-- Main Content -->
    <div class="main-content">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2><i class="fas fa-chair me-2"></i><?php echo __('tables_management', 'Quản lý bàn'); ?></h2>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#tableModal">
                <i class="fas fa-plus me-2"></i><?php echo __('add_table', 'Thêm bàn'); ?>
            </button>
        </div>
        
        <?php displayFlashMessage(); ?>
        
        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card bg-success text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h3 class="mb-0"><?php echo $tableStats['available_tables']; ?></h3>
                                <p class="mb-0"><?php echo __('table_available', 'Bàn trống'); ?></p>
                            </div>
                            <i class="fas fa-chair fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-danger text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h3 class="mb-0"><?php echo $tableStats['occupied_tables']; ?></h3>
                                <p class="mb-0"><?php echo __('table_occupied', 'Bàn có khách'); ?></p>
                            </div>
                            <i class="fas fa-users fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-warning text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h3 class="mb-0"><?php echo $tableStats['reserved_tables']; ?></h3>
                                <p class="mb-0"><?php echo __('table_reserved', 'Bàn đã đặt'); ?></p>
                            </div>
                            <i class="fas fa-bookmark fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-secondary text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h3 class="mb-0"><?php echo $tableStats['total_tables']; ?></h3>
                                <p class="mb-0"><?php echo __('total_tables', 'Tổng số bàn'); ?></p>
                            </div>
                            <i class="fas fa-list fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Search and Filter -->
        <div class="card mb-4">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-5">
                        <div class="form-outline">
                            <input type="text" id="search" name="search" class="form-control" 
                                   value="<?php echo htmlspecialchars($search); ?>">
                            <label class="form-label" for="search"><?php echo __('search_tables', 'Tìm kiếm bàn...'); ?></label>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <select id="status" name="status" class="form-select">
                            <option value=""><?php echo __('all_statuses', 'Tất cả trạng thái'); ?></option>
                            <option value="available" <?php echo $statusFilter === 'available' ? 'selected' : ''; ?>><?php echo __('table_available', 'Trống'); ?></option>
                            <option value="occupied" <?php echo $statusFilter === 'occupied' ? 'selected' : ''; ?>><?php echo __('table_occupied', 'Có khách'); ?></option>
                            <option value="reserved" <?php echo $statusFilter === 'reserved' ? 'selected' : ''; ?>><?php echo __('table_reserved', 'Đã đặt'); ?></option>
                            <option value="maintenance" <?php echo $statusFilter === 'maintenance' ? 'selected' : ''; ?>><?php echo __('table_maintenance', 'Bảo trì'); ?></option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <button type="submit" class="btn btn-outline-primary me-2">
                            <i class="fas fa-search me-1"></i><?php echo __('search', 'Tìm kiếm'); ?>
                        </button>
                        <a href="tables.php" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-1"></i><?php echo __('clear', 'Xóa'); ?>
                        </a>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Tables Table -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><?php echo __('table_list', 'Danh sách bàn'); ?></h5>
                <span class="badge bg-primary">
                    <?php echo __('total', 'Tổng cộng'); ?>: <?php echo $tablesData['total'] ?? 0; ?>
                </span>
            </div>
            <div class="card-body">
                <?php if (empty($tablesData['tables'])): ?>
                    <div class="text-center py-4">
                        <i class="fas fa-chair fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted"><?php echo __('no_tables_found', 'Không tìm thấy bàn'); ?></h5>
                        <p class="text-muted">
                            <?php echo ($search || $statusFilter) ? __('adjust_search_criteria', 'Thử điều chỉnh tiêu chí tìm kiếm.') : __('add_first_table', 'Bắt đầu bằng cách thêm bàn đầu tiên.'); ?>
                        </p>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th><?php echo __('table_number', 'Số bàn'); ?></th>
                                    <th><?php echo __('table_capacity', 'Sức chứa'); ?></th>
                                    <th><?php echo __('status', 'Trạng thái'); ?></th>
                                    <th><?php echo __('created_at', 'Ngày tạo'); ?></th>
                                    <th><?php echo __('actions', 'Thao tác'); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($tablesData['tables'] as $table): ?>
                                    <tr>
                                        <td><?php echo $table['table_id']; ?></td>
                                        <td>
                                            <strong><?php echo htmlspecialchars($table['table_number']); ?></strong>
                                        </td>
                                        <td>
                                            <i class="fas fa-users me-1"></i>
                                            <?php echo $table['capacity']; ?> <?php echo __('people', 'người'); ?>
                                        </td>
                                        <td>
                                            <span class="badge bg-<?php 
                                                echo $table['status'] === 'available' ? 'success' : 
                                                    ($table['status'] === 'occupied' ? 'danger' : 
                                                    ($table['status'] === 'reserved' ? 'warning' : 'secondary')); 
                                            ?>">
                                                <?php echo __('table_' . $table['status'], ucfirst($table['status'])); ?>
                                            </span>
                                        </td>
                                        <td><?php echo formatDate($table['created_at']); ?></td>
                                        <td>
                                            <a href="?edit=<?php echo $table['table_id']; ?>" 
                                               class="btn btn-sm btn-outline-primary btn-action me-1"
                                               data-bs-toggle="tooltip" title="<?php echo __('edit', 'Sửa'); ?>">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" 
                                                    class="btn btn-sm btn-outline-danger btn-action"
                                                    data-bs-toggle="tooltip" title="<?php echo __('delete', 'Xóa'); ?>"
                                                    onclick="confirmDelete(<?php echo $table['table_id']; ?>, '<?php echo htmlspecialchars($table['table_number']); ?>')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    <?php if ($tablesData['pages'] > 1): ?>
                        <div class="mt-3">
                            <?php 
                            $params = [];
                            if ($search) $params['search'] = $search;
                            if ($statusFilter) $params['status'] = $statusFilter;
                            echo generatePagination($tablesData['current_page'], $tablesData['pages'], 'tables.php', $params); 
                            ?>
                        </div>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- Table Modal -->
    <div class="modal fade" id="tableModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-chair me-2"></i>
                        <?php echo $editTable ? __('edit_table', 'Sửa bàn') : __('add_new_table', 'Thêm bàn mới'); ?>
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                        <input type="hidden" name="action" value="<?php echo $editTable ? 'update' : 'create'; ?>">
                        <?php if ($editTable): ?>
                            <input type="hidden" name="table_id" value="<?php echo $editTable['table_id']; ?>">
                        <?php endif; ?>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-outline mb-3">
                                    <input type="text" id="table_number" name="table_number" class="form-control" 
                                           value="<?php echo $editTable ? htmlspecialchars($editTable['table_number']) : ''; ?>" required>
                                    <label class="form-label" for="table_number"><?php echo __('table_number', 'Số bàn'); ?> *</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-outline mb-3">
                                    <input type="number" id="capacity" name="capacity" class="form-control" min="1" max="20"
                                           value="<?php echo $editTable ? $editTable['capacity'] : ''; ?>" required>
                                    <label class="form-label" for="capacity"><?php echo __('table_capacity', 'Sức chứa'); ?> *</label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="status" class="form-label"><?php echo __('status', 'Trạng thái'); ?></label>
                            <select id="status" name="status" class="form-select">
                                <option value="available" <?php echo (!$editTable || $editTable['status'] === 'available') ? 'selected' : ''; ?>><?php echo __('table_available', 'Trống'); ?></option>
                                <option value="occupied" <?php echo ($editTable && $editTable['status'] === 'occupied') ? 'selected' : ''; ?>><?php echo __('table_occupied', 'Có khách'); ?></option>
                                <option value="reserved" <?php echo ($editTable && $editTable['status'] === 'reserved') ? 'selected' : ''; ?>><?php echo __('table_reserved', 'Đã đặt'); ?></option>
                                <option value="maintenance" <?php echo ($editTable && $editTable['status'] === 'maintenance') ? 'selected' : ''; ?>><?php echo __('table_maintenance', 'Bảo trì'); ?></option>
                            </select>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?php echo __('cancel', 'Hủy'); ?></button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            <?php echo $editTable ? __('update', 'Cập nhật') : __('create', 'Tạo mới'); ?>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-exclamation-triangle me-2 text-danger"></i>
                        <?php echo __('confirm_delete', 'Xác nhận xóa'); ?>
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p><?php echo __('confirm_delete_table', 'Bạn có chắc chắn muốn xóa bàn'); ?> "<span id="deleteTableNumber"></span>"?</p>
                    <p class="text-danger small">
                        <i class="fas fa-warning me-1"></i>
                        <?php echo __('action_cannot_undone_table', 'Hành động này không thể hoàn tác. Bàn chỉ có thể xóa nếu không có đơn hàng liên quan.'); ?>
                    </p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?php echo __('cancel', 'Hủy'); ?></button>
                    <form method="POST" style="display: inline;">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                        <input type="hidden" name="action" value="delete">
                        <input type="hidden" name="table_id" id="deleteTableId">
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-trash me-2"></i><?php echo __('delete', 'Xóa'); ?>
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <!-- MDBootstrap JS -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/mdb-ui-kit/6.4.2/mdb.min.js"></script>
    <!-- Custom JS -->
    <script src="../../assets/js/app.js"></script>
    
    <script>
        // Show modal if editing
        <?php if ($editTable): ?>
        document.addEventListener('DOMContentLoaded', function() {
            const modal = new mdb.Modal(document.getElementById('tableModal'));
            modal.show();
        });
        <?php endif; ?>
        
        // Delete confirmation
        function confirmDelete(tableId, tableNumber) {
            document.getElementById('deleteTableId').value = tableId;
            document.getElementById('deleteTableNumber').textContent = tableNumber;
            const modal = new mdb.Modal(document.getElementById('deleteModal'));
            modal.show();
        }
        
        // Initialize tooltips
        document.addEventListener('DOMContentLoaded', function() {
            const tooltips = document.querySelectorAll('[data-bs-toggle="tooltip"]');
            tooltips.forEach(tooltip => {
                new mdb.Tooltip(tooltip);
            });
        });
    </script>
</body>
</html>
