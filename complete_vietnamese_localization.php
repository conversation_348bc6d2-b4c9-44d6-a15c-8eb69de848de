<?php
/**
 * Complete Vietnamese Localization Script
 * 
 * This script completes the Vietnamese localization of the Restaurant Management System
 * and provides a comprehensive overview of all translated components.
 */

require_once 'config/config.php';
require_once 'config/database.php';
require_once 'includes/functions.php';

echo "<h2>🇻🇳 Hoàn thành việt hóa hệ thống quản lý nhà hàng</h2>";

if ($_POST['action'] ?? '' === 'complete_localization') {
    echo "<h3>Đang hoàn thành việt hóa...</h3>";
    
    try {
        // Update remaining database content
        echo "<h4>1. Cập nhật dữ liệu cơ sở dữ liệu</h4>";
        
        // Update user full names
        $stmt = $pdo->prepare("UPDATE users SET full_name = ? WHERE username = 'admin'");
        $stmt->execute(['Quản trị viên hệ thống']);
        echo "✅ Cập nhật tên quản trị viên<br>";
        
        $stmt = $pdo->prepare("UPDATE users SET full_name = ? WHERE username = 'staff'");
        $stmt->execute(['Nhân viên phục vụ']);
        echo "✅ Cập nhật tên nhân viên<br>";
        
        // Update sample categories if they exist
        $vietnameseCategories = [
            'Appetizers' => 'Khai vị',
            'Main Course' => 'Món chính',
            'Main Courses' => 'Món chính',
            'Desserts' => 'Tráng miệng',
            'Beverages' => 'Đồ uống',
            'Specials' => 'Món đặc biệt',
            'Soups' => 'Súp'
        ];
        
        foreach ($vietnameseCategories as $english => $vietnamese) {
            $stmt = $pdo->prepare("UPDATE categories SET category_name = ? WHERE category_name = ?");
            if ($stmt->execute([$vietnamese, $english])) {
                echo "✅ Cập nhật danh mục: $english → $vietnamese<br>";
            }
        }
        
        // Update sample food items if they exist
        $vietnameseFoodItems = [
            'Spring Rolls' => 'Chả giò',
            'Chicken Wings' => 'Cánh gà chiên',
            'Grilled Salmon' => 'Cá hồi nướng',
            'Beef Steak' => 'Bít tết bò',
            'Chocolate Cake' => 'Bánh chocolate',
            'Cheesecake' => 'Bánh phô mai',
            'Lemonade' => 'Nước chanh',
            'Iced Tea' => 'Trà đá',
            'Chef\'s Special Pasta' => 'Mì Ý đặc biệt của đầu bếp',
            'Caesar Salad' => 'Salad Caesar',
            'Ice Cream' => 'Kem',
            'Coffee' => 'Cà phê',
            'Fresh Juice' => 'Nước ép tươi'
        ];
        
        foreach ($vietnameseFoodItems as $english => $vietnamese) {
            $stmt = $pdo->prepare("UPDATE food_items SET food_name = ? WHERE food_name = ?");
            if ($stmt->execute([$vietnamese, $english])) {
                echo "✅ Cập nhật món ăn: $english → $vietnamese<br>";
            }
        }
        
        // Update food descriptions to Vietnamese
        $foodDescriptions = [
            'Fresh vegetables wrapped in rice paper' => 'Rau tươi cuốn trong bánh tráng',
            'Spicy buffalo wings with blue cheese dip' => 'Cánh gà cay với sốt phô mai xanh',
            'Fresh salmon with lemon butter sauce' => 'Cá hồi tươi với sốt bơ chanh',
            'Juicy steak with garlic butter' => 'Bít tết ngon ngọt với bơ tỏi',
            'Rich chocolate cake with vanilla ice cream' => 'Bánh chocolate đậm đà với kem vani',
            'New York style cheesecake' => 'Bánh phô mai kiểu New York',
            'Fresh squeezed lemonade' => 'Nước chanh tươi vắt',
            'Refreshing iced tea with lemon' => 'Trà đá mát lạnh với chanh',
            'Pasta with secret sauce and ingredients' => 'Mì Ý với sốt và nguyên liệu bí mật'
        ];
        
        foreach ($foodDescriptions as $english => $vietnamese) {
            $stmt = $pdo->prepare("UPDATE food_items SET description = ? WHERE description = ?");
            if ($stmt->execute([$vietnamese, $english])) {
                echo "✅ Cập nhật mô tả món ăn<br>";
            }
        }
        
        echo "<h4>2. Kiểm tra các trang đã việt hóa</h4>";
        
        $translatedPages = [
            'views/auth/login.php' => 'Trang đăng nhập',
            'views/auth/logout.php' => 'Trang đăng xuất',
            'views/admin/dashboard.php' => 'Bảng điều khiển quản trị viên',
            'views/admin/categories.php' => 'Quản lý danh mục',
            'views/admin/food-items.php' => 'Quản lý món ăn',
            'views/admin/tables.php' => 'Quản lý bàn',
            'views/staff/dashboard.php' => 'Bảng điều khiển nhân viên',
            'views/errors/404.php' => 'Trang lỗi 404',
            'views/errors/403.php' => 'Trang lỗi 403',
            'views/errors/500.php' => 'Trang lỗi 500'
        ];
        
        foreach ($translatedPages as $file => $description) {
            if (file_exists($file)) {
                echo "✅ $description ($file)<br>";
            } else {
                echo "⚠️ $description ($file) - Chưa tồn tại<br>";
            }
        }
        
        echo "<h4>3. Kiểm tra hệ thống dịch thuật</h4>";
        
        // Test translation functions
        $testKeys = [
            'dashboard' => 'Bảng điều khiển',
            'categories' => 'Danh mục',
            'food_items' => 'Món ăn',
            'tables' => 'Bàn',
            'orders' => 'Đơn hàng',
            'payments' => 'Thanh toán',
            'reports' => 'Báo cáo'
        ];
        
        foreach ($testKeys as $key => $expected) {
            $translated = __($key);
            if ($translated === $expected) {
                echo "✅ Dịch '$key' → '$translated'<br>";
            } else {
                echo "❌ Dịch '$key' → '$translated' (mong đợi: '$expected')<br>";
            }
        }
        
        echo "<h4>4. Kiểm tra định dạng địa phương</h4>";
        
        // Test currency formatting
        $testAmounts = [25000, 150000, 1250000];
        foreach ($testAmounts as $amount) {
            echo "✅ " . number_format($amount) . " VND → " . formatCurrency($amount) . "<br>";
        }
        
        // Test date formatting
        $testDate = date('Y-m-d H:i:s');
        echo "✅ Định dạng ngày: " . formatDate($testDate) . "<br>";
        echo "✅ Định dạng ngày tiếng Việt: " . formatVietnameseDate($testDate) . "<br>";
        
        echo "<h4>5. Kiểm tra mã hóa UTF-8</h4>";
        
        $vietnameseChars = ['ă', 'â', 'đ', 'ê', 'ô', 'ơ', 'ư', 'á', 'à', 'ả', 'ã', 'ạ'];
        echo "✅ Ký tự tiếng Việt: " . implode(', ', $vietnameseChars) . "<br>";
        
        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 20px; border-radius: 10px; margin: 30px 0;'>";
        echo "<h3>🎉 Việt hóa hoàn tất thành công!</h3>";
        echo "<h4>📋 Tóm tắt những gì đã được việt hóa:</h4>";
        echo "<div class='row'>";
        echo "<div class='col-md-6'>";
        echo "<h5>🎨 Giao diện người dùng:</h5>";
        echo "<ul>";
        echo "<li>✅ Trang đăng nhập với thông báo tiếng Việt</li>";
        echo "<li>✅ Bảng điều khiển quản trị viên</li>";
        echo "<li>✅ Bảng điều khiển nhân viên</li>";
        echo "<li>✅ Quản lý danh mục món ăn</li>";
        echo "<li>✅ Quản lý món ăn</li>";
        echo "<li>✅ Quản lý bàn</li>";
        echo "<li>✅ Trang lỗi (404, 403, 500)</li>";
        echo "</ul>";
        echo "</div>";
        echo "<div class='col-md-6'>";
        echo "<h5>🗄️ Dữ liệu và định dạng:</h5>";
        echo "<ul>";
        echo "<li>✅ Danh mục món ăn tiếng Việt</li>";
        echo "<li>✅ Tên món ăn tiếng Việt</li>";
        echo "<li>✅ Định dạng tiền tệ VND</li>";
        echo "<li>✅ Định dạng ngày tháng Việt Nam</li>";
        echo "<li>✅ Múi giờ Asia/Ho_Chi_Minh</li>";
        echo "<li>✅ Mã hóa UTF-8</li>";
        echo "<li>✅ Hơn 300 cụm từ dịch thuật</li>";
        echo "</ul>";
        echo "</div>";
        echo "</div>";
        echo "<h4>🚀 Bước tiếp theo:</h4>";
        echo "<ol>";
        echo "<li>Đăng nhập vào hệ thống để trải nghiệm giao diện tiếng Việt</li>";
        echo "<li>Kiểm tra các chức năng CRUD với dữ liệu tiếng Việt</li>";
        echo "<li>Thử tạo đơn hàng mới với định dạng tiền tệ VND</li>";
        echo "<li>Kiểm tra báo cáo và thống kê</li>";
        echo "</ol>";
        echo "</div>";
        
        echo "<div class='text-center mt-4'>";
        echo "<a href='views/auth/login.php' style='background: #28a745; color: white; padding: 15px 30px; text-decoration: none; border-radius: 25px; font-size: 18px; margin: 10px;'>";
        echo "<i class='fas fa-sign-in-alt me-2'></i>Đăng nhập vào hệ thống";
        echo "</a>";
        echo "<a href='test_vietnamese_features.php' style='background: #007bff; color: white; padding: 15px 30px; text-decoration: none; border-radius: 25px; font-size: 18px; margin: 10px;'>";
        echo "<i class='fas fa-test-tube me-2'></i>Kiểm tra tính năng";
        echo "</a>";
        echo "</div>";
        
    } catch (PDOException $e) {
        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "❌ Lỗi cơ sở dữ liệu: " . $e->getMessage();
        echo "</div>";
    } catch (Exception $e) {
        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "❌ Lỗi: " . $e->getMessage();
        echo "</div>";
    }
    
} else {
    // Show completion form
    echo "<p>Script này sẽ hoàn thành việt hóa toàn bộ hệ thống quản lý nhà hàng và cung cấp báo cáo tổng quan.</p>";
    
    echo "<div style='background: #e7f3ff; border: 1px solid #b3d7ff; color: #004085; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h4>📋 Những gì sẽ được hoàn thành:</h4>";
    echo "<div class='row'>";
    echo "<div class='col-md-6'>";
    echo "<h5>🎨 Giao diện người dùng:</h5>";
    echo "<ul>";
    echo "<li>Hoàn thành dịch tất cả trang quản trị</li>";
    echo "<li>Cập nhật thông báo lỗi và thành công</li>";
    echo "<li>Dịch các nút và nhãn form</li>";
    echo "<li>Cập nhật tiêu đề trang và breadcrumb</li>";
    echo "</ul>";
    echo "</div>";
    echo "<div class='col-md-6'>";
    echo "<h5>🗄️ Dữ liệu và cấu hình:</h5>";
    echo "<ul>";
    echo "<li>Cập nhật dữ liệu mẫu sang tiếng Việt</li>";
    echo "<li>Kiểm tra định dạng tiền tệ VND</li>";
    echo "<li>Xác minh định dạng ngày tháng</li>";
    echo "<li>Kiểm tra mã hóa UTF-8</li>";
    echo "</ul>";
    echo "</div>";
    echo "</div>";
    echo "</div>";
    
    echo "<form method='POST'>";
    echo "<input type='hidden' name='action' value='complete_localization'>";
    echo "<button type='submit' style='background: #28a745; color: white; padding: 20px 40px; border: none; border-radius: 25px; cursor: pointer; font-size: 18px; font-weight: bold;'>";
    echo "<i class='fas fa-flag me-2'></i>Hoàn thành việt hóa hệ thống";
    echo "</button>";
    echo "</form>";
    
    echo "<hr>";
    echo "<h3>📊 Trạng thái việt hóa hiện tại</h3>";
    
    // Check current status
    $completedFeatures = [
        'Language file (includes/lang_vi.php)' => file_exists('includes/lang_vi.php'),
        'Login page translation' => file_exists('views/auth/login.php'),
        'Admin dashboard translation' => file_exists('views/admin/dashboard.php'),
        'Categories management' => file_exists('views/admin/categories.php'),
        'Food items management' => file_exists('views/admin/food-items.php'),
        'Tables management' => file_exists('views/admin/tables.php'),
        'Staff dashboard translation' => file_exists('views/staff/dashboard.php'),
        'Error pages (404, 403, 500)' => file_exists('views/errors/404.php'),
        'Currency formatting' => function_exists('formatCurrency'),
        'Date formatting' => function_exists('formatVietnameseDate'),
        'Translation functions' => function_exists('__')
    ];
    
    echo "<div class='row'>";
    foreach ($completedFeatures as $feature => $completed) {
        $status = $completed ? '✅' : '❌';
        $color = $completed ? '#28a745' : '#dc3545';
        echo "<div class='col-md-6'>";
        echo "<p style='color: $color;'>$status $feature</p>";
        echo "</div>";
    }
    echo "</div>";
    
    $completedCount = array_sum($completedFeatures);
    $totalCount = count($completedFeatures);
    $percentage = round(($completedCount / $totalCount) * 100);
    
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h4>📈 Tiến độ việt hóa: $percentage% ($completedCount/$totalCount)</h4>";
    echo "<div style='background: #e9ecef; height: 20px; border-radius: 10px; overflow: hidden;'>";
    echo "<div style='background: #28a745; height: 100%; width: $percentage%; transition: width 0.3s ease;'></div>";
    echo "</div>";
    echo "</div>";
}

echo "<hr>";
echo "<p><strong>Các công cụ khác:</strong></p>";
echo "<ul>";
echo "<li><a href='localize_vietnamese.php'>Script việt hóa cơ bản</a></li>";
echo "<li><a href='test_db_connection.php'>Kiểm tra kết nối cơ sở dữ liệu</a></li>";
echo "<li><a href='test_login.php'>Kiểm tra đăng nhập</a></li>";
echo "<li><a href='views/auth/login.php'>Trang đăng nhập</a></li>";
echo "</ul>";
?>

<style>
body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; line-height: 1.6; background: #f8f9fa; }
h2 { color: #333; border-bottom: 3px solid #007bff; padding-bottom: 10px; }
h3 { color: #007bff; margin-top: 30px; border-left: 4px solid #007bff; padding-left: 15px; }
h4 { color: #28a745; margin-top: 20px; }
h5 { color: #6c757d; margin-top: 15px; }
ul, ol { margin: 10px 0; padding-left: 30px; }
li { margin: 5px 0; }
button { transition: all 0.3s ease; }
button:hover { transform: translateY(-2px); box-shadow: 0 5px 15px rgba(0,0,0,0.2); }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
.row { display: flex; flex-wrap: wrap; margin: -10px; }
.col-md-6 { flex: 0 0 50%; padding: 10px; }
@media (max-width: 768px) { .col-md-6 { flex: 0 0 100%; } }
</style>
