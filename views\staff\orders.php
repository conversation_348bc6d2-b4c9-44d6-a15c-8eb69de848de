<?php
/**
 * Staff Orders Management Page
 * 
 * Staff interface for viewing and managing orders with real-time updates
 * and status management functionality.
 */

require_once '../../config/config.php';
require_once '../../config/database.php';
require_once '../../includes/functions.php';
require_once '../../models/Order.php';
require_once '../../models/Table.php';

// Check staff access
requireLogin();

// Initialize models
$orderModel = new Order();
$tableModel = new Table();

// Handle quick status updates via AJAX
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['ajax_action'])) {
    header('Content-Type: application/json');
    
    if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
        echo json_encode(['success' => false, 'message' => __('invalid_csrf_token', 'Token bảo mật không hợp lệ.')]);
        exit();
    }
    
    $action = $_POST['ajax_action'];
    
    if ($action === 'update_status') {
        $orderId = (int)($_POST['order_id'] ?? 0);
        $newStatus = sanitizeInput($_POST['status'] ?? '');
        $validStatuses = ['pending', 'processing', 'completed', 'cancelled'];
        
        if ($orderId > 0 && in_array($newStatus, $validStatuses)) {
            if ($orderModel->updateOrderStatus($orderId, $newStatus)) {
                // Update table status if order is completed or cancelled
                if ($newStatus === 'completed' || $newStatus === 'cancelled') {
                    $order = $orderModel->getOrderById($orderId);
                    if ($order) {
                        $tableModel->updateTableStatus($order['table_id'], 'available');
                    }
                }
                
                logActivity("Cập nhật trạng thái đơn hàng #$orderId thành $newStatus", $_SESSION['user_id']);
                echo json_encode(['success' => true, 'message' => __('order_status_updated', 'Cập nhật trạng thái thành công.')]);
            } else {
                echo json_encode(['success' => false, 'message' => __('order_status_update_failed', 'Cập nhật trạng thái thất bại.')]);
            }
        } else {
            echo json_encode(['success' => false, 'message' => __('invalid_data', 'Dữ liệu không hợp lệ.')]);
        }
    }
    
    exit();
}

// Get filter parameters
$page = (int)($_GET['page'] ?? 1);
$search = sanitizeInput($_GET['search'] ?? '');
$statusFilter = sanitizeInput($_GET['status'] ?? '');
$dateFilter = sanitizeInput($_GET['date'] ?? '');

// Build filter conditions
$filters = [];
if ($search) $filters['search'] = $search;
if ($statusFilter) $filters['status'] = $statusFilter;
if ($dateFilter) $filters['date'] = $dateFilter;

// Get orders with filters
if (!empty($filters)) {
    $ordersData = $orderModel->searchOrders($filters, $page);
} else {
    $ordersData = $orderModel->getAllOrders($page, ITEMS_PER_PAGE);
}

// Get order statistics for today
$todayStats = $orderModel->getTodayOrderStats();

$pageTitle = __('orders_management', 'Quản lý đơn hàng') . ' - ' . APP_NAME;
?>
<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?></title>
    
    <!-- MDBootstrap CSS -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/mdb-ui-kit/6.4.2/mdb.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="../../assets/css/custom.css" rel="stylesheet">
    
    <style>
        .order-card {
            transition: all 0.3s ease;
            border-left: 4px solid transparent;
        }
        .order-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .order-card.status-pending { border-left-color: #ffc107; }
        .order-card.status-processing { border-left-color: #17a2b8; }
        .order-card.status-completed { border-left-color: #28a745; }
        .order-card.status-cancelled { border-left-color: #dc3545; }
        
        .status-badge {
            font-size: 0.8rem;
            padding: 4px 8px;
            border-radius: 12px;
        }
        
        .quick-actions {
            display: flex;
            gap: 5px;
        }
        
        .auto-refresh-indicator {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            background: rgba(0, 123, 255, 0.9);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9rem;
            display: none;
        }
        
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
        }
        
        .filter-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <!-- Auto-refresh indicator -->
    <div class="auto-refresh-indicator" id="refreshIndicator">
        <i class="fas fa-sync-alt fa-spin me-2"></i><?php echo __('refreshing_data', 'Đang cập nhật dữ liệu...'); ?>
    </div>
    
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="dashboard.php">
                <i class="fas fa-utensils me-2"></i><?php echo APP_NAME; ?>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-1"></i><?php echo __('dashboard', 'Bảng điều khiển'); ?>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="tables.php">
                            <i class="fas fa-chair me-1"></i><?php echo __('tables', 'Bàn'); ?>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="orders.php">
                            <i class="fas fa-receipt me-1"></i><?php echo __('orders', 'Đơn hàng'); ?>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="new-order.php">
                            <i class="fas fa-plus me-1"></i><?php echo __('new_order', 'Đơn hàng mới'); ?>
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle me-1"></i>
                            <?php echo htmlspecialchars($_SESSION['full_name']); ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="profile.php">
                                <i class="fas fa-user me-2"></i><?php echo __('profile', 'Hồ sơ'); ?>
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../auth/logout.php">
                                <i class="fas fa-sign-out-alt me-2"></i><?php echo __('logout', 'Đăng xuất'); ?>
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    
    <!-- Main Content -->
    <div class="container-fluid mt-4">
        <!-- Page Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2><i class="fas fa-receipt me-2"></i><?php echo __('orders_management', 'Quản lý đơn hàng'); ?></h2>
            <div>
                <button type="button" class="btn btn-outline-primary me-2" onclick="refreshOrders()">
                    <i class="fas fa-sync-alt me-1"></i><?php echo __('refresh', 'Làm mới'); ?>
                </button>
                <a href="new-order.php" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i><?php echo __('new_order', 'Đơn hàng mới'); ?>
                </a>
            </div>
        </div>
        
        <?php displayFlashMessage(); ?>
        
        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <i class="fas fa-clock fa-2x mb-2"></i>
                        <h3><?php echo $todayStats['pending'] ?? 0; ?></h3>
                        <p class="mb-0"><?php echo __('pending_orders', 'Đơn chờ xử lý'); ?></p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <i class="fas fa-cog fa-2x mb-2"></i>
                        <h3><?php echo $todayStats['processing'] ?? 0; ?></h3>
                        <p class="mb-0"><?php echo __('processing_orders', 'Đang xử lý'); ?></p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <i class="fas fa-check fa-2x mb-2"></i>
                        <h3><?php echo $todayStats['completed'] ?? 0; ?></h3>
                        <p class="mb-0"><?php echo __('completed_orders', 'Đã hoàn thành'); ?></p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <i class="fas fa-receipt fa-2x mb-2"></i>
                        <h3><?php echo $todayStats['total'] ?? 0; ?></h3>
                        <p class="mb-0"><?php echo __('total_orders_today', 'Tổng đơn hôm nay'); ?></p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Filters -->
        <div class="filter-section">
            <form method="GET" class="row g-3">
                <div class="col-md-3">
                    <div class="form-outline">
                        <input type="text" id="search" name="search" class="form-control" 
                               value="<?php echo htmlspecialchars($search); ?>">
                        <label class="form-label" for="search"><?php echo __('search_orders', 'Tìm kiếm đơn hàng...'); ?></label>
                    </div>
                </div>
                <div class="col-md-2">
                    <select id="status" name="status" class="form-select">
                        <option value=""><?php echo __('all_statuses', 'Tất cả trạng thái'); ?></option>
                        <option value="pending" <?php echo $statusFilter === 'pending' ? 'selected' : ''; ?>><?php echo __('order_status_pending', 'Chờ xử lý'); ?></option>
                        <option value="processing" <?php echo $statusFilter === 'processing' ? 'selected' : ''; ?>><?php echo __('order_status_processing', 'Đang xử lý'); ?></option>
                        <option value="completed" <?php echo $statusFilter === 'completed' ? 'selected' : ''; ?>><?php echo __('order_status_completed', 'Hoàn thành'); ?></option>
                        <option value="cancelled" <?php echo $statusFilter === 'cancelled' ? 'selected' : ''; ?>><?php echo __('order_status_cancelled', 'Đã hủy'); ?></option>
                    </select>
                </div>
                <div class="col-md-2">
                    <input type="date" id="date" name="date" class="form-control" 
                           value="<?php echo htmlspecialchars($dateFilter); ?>">
                </div>
                <div class="col-md-3">
                    <button type="submit" class="btn btn-outline-primary me-2">
                        <i class="fas fa-search me-1"></i><?php echo __('search', 'Tìm kiếm'); ?>
                    </button>
                    <a href="orders.php" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-1"></i><?php echo __('clear', 'Xóa'); ?>
                    </a>
                </div>
                <div class="col-md-2">
                    <div class="form-check form-switch">
                        <input class="form-check-input" type="checkbox" id="autoRefresh" checked>
                        <label class="form-check-label" for="autoRefresh">
                            <?php echo __('auto_refresh', 'Tự động làm mới'); ?>
                        </label>
                    </div>
                </div>
            </form>
        </div>
        
        <!-- Orders List -->
        <div class="row" id="ordersContainer">
            <?php if (empty($ordersData['orders'])): ?>
                <div class="col-12">
                    <div class="text-center py-5">
                        <i class="fas fa-receipt fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted"><?php echo __('no_orders_found', 'Không tìm thấy đơn hàng'); ?></h5>
                        <p class="text-muted">
                            <?php echo ($search || $statusFilter || $dateFilter) ? __('adjust_search_criteria', 'Thử điều chỉnh tiêu chí tìm kiếm.') : __('no_orders_today', 'Chưa có đơn hàng nào hôm nay.'); ?>
                        </p>
                        <a href="new-order.php" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i><?php echo __('create_first_order', 'Tạo đơn hàng đầu tiên'); ?>
                        </a>
                    </div>
                </div>
            <?php else: ?>
                <?php foreach ($ordersData['orders'] as $order): ?>
                    <div class="col-lg-6 col-xl-4 mb-4">
                        <div class="card order-card status-<?php echo $order['status']; ?>" data-order-id="<?php echo $order['order_id']; ?>">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h6 class="mb-0">
                                    <i class="fas fa-receipt me-2"></i><?php echo __('order', 'Đơn hàng'); ?> #<?php echo $order['order_id']; ?>
                                </h6>
                                <span class="status-badge bg-<?php 
                                    echo $order['status'] === 'pending' ? 'warning' : 
                                        ($order['status'] === 'processing' ? 'info' : 
                                        ($order['status'] === 'completed' ? 'success' : 'danger')); 
                                ?>">
                                    <?php echo __('order_status_' . $order['status'], ucfirst($order['status'])); ?>
                                </span>
                            </div>
                            <div class="card-body">
                                <div class="row mb-2">
                                    <div class="col-6">
                                        <small class="text-muted"><?php echo __('table', 'Bàn'); ?>:</small><br>
                                        <strong><?php echo __('table', 'Bàn'); ?> <?php echo htmlspecialchars($order['table_number']); ?></strong>
                                    </div>
                                    <div class="col-6">
                                        <small class="text-muted"><?php echo __('staff', 'Nhân viên'); ?>:</small><br>
                                        <strong><?php echo htmlspecialchars($order['staff_name']); ?></strong>
                                    </div>
                                </div>
                                
                                <?php if ($order['customer_name']): ?>
                                    <div class="mb-2">
                                        <small class="text-muted"><?php echo __('customer', 'Khách hàng'); ?>:</small><br>
                                        <strong><?php echo htmlspecialchars($order['customer_name']); ?></strong>
                                    </div>
                                <?php endif; ?>
                                
                                <div class="mb-2">
                                    <small class="text-muted"><?php echo __('order_time', 'Giờ đặt'); ?>:</small><br>
                                    <strong><?php echo formatVietnameseDate($order['created_at']); ?></strong>
                                </div>
                                
                                <div class="mb-3">
                                    <small class="text-muted"><?php echo __('total_amount', 'Tổng tiền'); ?>:</small><br>
                                    <strong class="text-primary"><?php echo formatCurrency($order['total_amount']); ?></strong>
                                </div>
                                
                                <!-- Quick Status Update -->
                                <?php if ($order['status'] !== 'cancelled'): ?>
                                    <div class="mb-3">
                                        <select class="form-select form-select-sm status-select" data-order-id="<?php echo $order['order_id']; ?>">
                                            <option value="pending" <?php echo $order['status'] === 'pending' ? 'selected' : ''; ?>><?php echo __('order_status_pending', 'Chờ xử lý'); ?></option>
                                            <option value="processing" <?php echo $order['status'] === 'processing' ? 'selected' : ''; ?>><?php echo __('order_status_processing', 'Đang xử lý'); ?></option>
                                            <option value="completed" <?php echo $order['status'] === 'completed' ? 'selected' : ''; ?>><?php echo __('order_status_completed', 'Hoàn thành'); ?></option>
                                            <option value="cancelled" <?php echo $order['status'] === 'cancelled' ? 'selected' : ''; ?>><?php echo __('order_status_cancelled', 'Đã hủy'); ?></option>
                                        </select>
                                    </div>
                                <?php endif; ?>
                                
                                <!-- Action Buttons -->
                                <div class="quick-actions">
                                    <a href="order-details.php?id=<?php echo $order['order_id']; ?>" 
                                       class="btn btn-sm btn-outline-primary flex-fill">
                                        <i class="fas fa-eye me-1"></i><?php echo __('view', 'Xem'); ?>
                                    </a>
                                    <?php if ($order['status'] === 'completed'): ?>
                                        <a href="payment.php?order_id=<?php echo $order['order_id']; ?>" 
                                           class="btn btn-sm btn-success flex-fill">
                                            <i class="fas fa-credit-card me-1"></i><?php echo __('pay', 'Thanh toán'); ?>
                                        </a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
        
        <!-- Pagination -->
        <?php if ($ordersData['pages'] > 1): ?>
            <div class="mt-4">
                <?php 
                $params = [];
                if ($search) $params['search'] = $search;
                if ($statusFilter) $params['status'] = $statusFilter;
                if ($dateFilter) $params['date'] = $dateFilter;
                echo generatePagination($ordersData['current_page'], $ordersData['pages'], 'orders.php', $params); 
                ?>
            </div>
        <?php endif; ?>
    </div>
    
    <!-- MDBootstrap JS -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/mdb-ui-kit/6.4.2/mdb.min.js"></script>
    <!-- Custom JS -->
    <script src="../../assets/js/app.js"></script>
    
    <script>
        let autoRefreshInterval;
        
        // Status update functionality
        document.querySelectorAll('.status-select').forEach(select => {
            select.addEventListener('change', function() {
                const orderId = this.dataset.orderId;
                const newStatus = this.value;
                const originalStatus = this.querySelector('option[selected]')?.value || this.options[0].value;
                
                if (newStatus !== originalStatus) {
                    updateOrderStatus(orderId, newStatus, this);
                }
            });
        });
        
        function updateOrderStatus(orderId, newStatus, selectElement) {
            const formData = new FormData();
            formData.append('ajax_action', 'update_status');
            formData.append('order_id', orderId);
            formData.append('status', newStatus);
            formData.append('csrf_token', '<?php echo generateCSRFToken(); ?>');
            
            fetch('orders.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Update the card's status class
                    const orderCard = selectElement.closest('.order-card');
                    orderCard.className = orderCard.className.replace(/status-\w+/, `status-${newStatus}`);
                    
                    // Update status badge
                    const statusBadge = orderCard.querySelector('.status-badge');
                    const statusNames = {
                        'pending': '<?php echo __('order_status_pending', 'Chờ xử lý'); ?>',
                        'processing': '<?php echo __('order_status_processing', 'Đang xử lý'); ?>',
                        'completed': '<?php echo __('order_status_completed', 'Hoàn thành'); ?>',
                        'cancelled': '<?php echo __('order_status_cancelled', 'Đã hủy'); ?>'
                    };
                    const statusColors = {
                        'pending': 'bg-warning',
                        'processing': 'bg-info',
                        'completed': 'bg-success',
                        'cancelled': 'bg-danger'
                    };
                    
                    statusBadge.textContent = statusNames[newStatus];
                    statusBadge.className = `status-badge ${statusColors[newStatus]}`;
                    
                    // Show success message
                    showToast('success', data.message);
                    
                    // Refresh stats
                    setTimeout(refreshStats, 1000);
                } else {
                    // Revert select to original value
                    selectElement.value = selectElement.querySelector('option[selected]')?.value || selectElement.options[0].value;
                    showToast('error', data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                selectElement.value = selectElement.querySelector('option[selected]')?.value || selectElement.options[0].value;
                showToast('error', '<?php echo __('update_failed', 'Cập nhật thất bại.'); ?>');
            });
        }
        
        function refreshOrders() {
            const indicator = document.getElementById('refreshIndicator');
            indicator.style.display = 'block';
            
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        }
        
        function refreshStats() {
            // This would typically make an AJAX call to get updated stats
            // For now, we'll just refresh the page after a delay
            setTimeout(() => {
                if (document.getElementById('autoRefresh').checked) {
                    window.location.reload();
                }
            }, 2000);
        }
        
        function showToast(type, message) {
            const toast = document.createElement('div');
            toast.className = `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show position-fixed`;
            toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            toast.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.body.appendChild(toast);
            
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.remove();
                }
            }, 5000);
        }
        
        // Auto-refresh functionality
        function startAutoRefresh() {
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
            }
            
            autoRefreshInterval = setInterval(() => {
                if (document.getElementById('autoRefresh').checked) {
                    const indicator = document.getElementById('refreshIndicator');
                    indicator.style.display = 'block';
                    
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000);
                }
            }, 30000); // 30 seconds
        }
        
        // Initialize auto-refresh
        document.addEventListener('DOMContentLoaded', function() {
            startAutoRefresh();
            
            document.getElementById('autoRefresh').addEventListener('change', function() {
                if (this.checked) {
                    startAutoRefresh();
                } else {
                    if (autoRefreshInterval) {
                        clearInterval(autoRefreshInterval);
                    }
                }
            });
        });
    </script>
</body>
</html>
