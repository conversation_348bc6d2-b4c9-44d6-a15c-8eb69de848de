<?php
/**
 * Password Reset Page
 * 
 * Allows users to reset their password using a secure token.
 * Validates token and provides secure password reset functionality.
 */

require_once '../../config/config.php';
require_once '../../config/database.php';
require_once '../../includes/functions.php';
require_once '../../models/User.php';

// Initialize user model
$userModel = new User();

// Get token from URL
$token = sanitizeInput($_GET['token'] ?? '');
$validToken = false;
$tokenUser = null;

if ($token) {
    // Validate token
    $tokenUser = $userModel->validatePasswordResetToken($token);
    $validToken = ($tokenUser !== false);
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && $validToken) {
    if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
        setFlashMessage('error', __('invalid_csrf_token', 'Token bảo mật không hợp lệ. Vui lòng thử lại.'));
    } else {
        $newPassword = $_POST['new_password'] ?? '';
        $confirmPassword = $_POST['confirm_password'] ?? '';
        
        // Validation
        $errors = [];
        if (empty($newPassword)) {
            $errors[] = __('new_password_required', 'Mật khẩu mới là bắt buộc.');
        }
        if (strlen($newPassword) < 6) {
            $errors[] = __('password_min_length', 'Mật khẩu phải có ít nhất 6 ký tự.');
        }
        if ($newPassword !== $confirmPassword) {
            $errors[] = __('password_mismatch', 'Mật khẩu xác nhận không khớp.');
        }
        
        if (empty($errors)) {
            if ($userModel->resetPassword($tokenUser['user_id'], $newPassword, $token)) {
                setFlashMessage('success', __('password_reset_success', 'Đặt lại mật khẩu thành công. Bạn có thể đăng nhập với mật khẩu mới.'));
                logActivity("Đặt lại mật khẩu thành công", $tokenUser['user_id']);
                
                // Redirect to login page after 3 seconds
                header("refresh:3;url=login.php");
            } else {
                setFlashMessage('error', __('password_reset_failed', 'Đặt lại mật khẩu thất bại. Vui lòng thử lại.'));
            }
        } else {
            setFlashMessage('error', implode('<br>', $errors));
        }
    }
}

$pageTitle = __('reset_password', 'Đặt lại mật khẩu') . ' - ' . APP_NAME;
?>
<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?></title>
    
    <!-- MDBootstrap CSS -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/mdb-ui-kit/6.4.2/mdb.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Roboto', sans-serif;
        }
        .reset-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            max-width: 500px;
            width: 100%;
            margin: 20px;
        }
        .reset-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px 30px;
            text-align: center;
        }
        .reset-body {
            padding: 40px 30px;
        }
        .brand-logo {
            font-size: 3rem;
            margin-bottom: 20px;
        }
        .form-outline {
            margin-bottom: 20px;
        }
        .btn-reset {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        .btn-reset:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        .password-requirements {
            background: #e7f3ff;
            border: 1px solid #b3d7ff;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .password-requirements h6 {
            color: #004085;
            margin-bottom: 10px;
        }
        .password-requirements ul {
            color: #004085;
            margin-bottom: 0;
            padding-left: 20px;
        }
        .token-info {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
            text-align: center;
        }
        .token-invalid {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 10px;
            padding: 30px;
            text-align: center;
        }
        .password-strength {
            height: 5px;
            border-radius: 3px;
            margin-top: 5px;
            transition: all 0.3s ease;
        }
        .strength-weak { background: #dc3545; }
        .strength-medium { background: #ffc107; }
        .strength-strong { background: #28a745; }
    </style>
</head>
<body>
    <div class="reset-container">
        <!-- Header -->
        <div class="reset-header">
            <div class="brand-logo">
                <i class="fas fa-utensils"></i>
            </div>
            <h3><?php echo APP_NAME; ?></h3>
            <p class="mb-0"><?php echo __('reset_password', 'Đặt lại mật khẩu'); ?></p>
        </div>
        
        <!-- Body -->
        <div class="reset-body">
            <?php displayFlashMessage(); ?>
            
            <?php if (!$token): ?>
                <!-- No Token -->
                <div class="token-invalid">
                    <i class="fas fa-exclamation-triangle fa-3x text-danger mb-3"></i>
                    <h5 class="text-danger"><?php echo __('invalid_reset_link', 'Liên kết đặt lại không hợp lệ'); ?></h5>
                    <p class="text-muted">
                        <?php echo __('invalid_reset_link_desc', 'Liên kết đặt lại mật khẩu không hợp lệ hoặc đã hết hạn.'); ?>
                    </p>
                    <a href="forgot-password.php" class="btn btn-primary">
                        <i class="fas fa-redo me-2"></i><?php echo __('request_new_link', 'Yêu cầu liên kết mới'); ?>
                    </a>
                </div>
            <?php elseif (!$validToken): ?>
                <!-- Invalid/Expired Token -->
                <div class="token-invalid">
                    <i class="fas fa-clock fa-3x text-warning mb-3"></i>
                    <h5 class="text-warning"><?php echo __('expired_reset_link', 'Liên kết đã hết hạn'); ?></h5>
                    <p class="text-muted">
                        <?php echo __('expired_reset_link_desc', 'Liên kết đặt lại mật khẩu đã hết hạn hoặc đã được sử dụng.'); ?>
                    </p>
                    <a href="forgot-password.php" class="btn btn-primary">
                        <i class="fas fa-redo me-2"></i><?php echo __('request_new_link', 'Yêu cầu liên kết mới'); ?>
                    </a>
                </div>
            <?php else: ?>
                <!-- Valid Token - Show Reset Form -->
                <div class="text-center mb-4">
                    <h4><?php echo __('create_new_password', 'Tạo mật khẩu mới'); ?></h4>
                    <p class="text-muted">
                        <?php echo __('reset_password_for', 'Đặt lại mật khẩu cho'); ?>: 
                        <strong><?php echo htmlspecialchars($tokenUser['username']); ?></strong>
                    </p>
                </div>
                
                <!-- Token Info -->
                <div class="token-info">
                    <small>
                        <i class="fas fa-info-circle me-2"></i>
                        <?php echo __('secure_reset_process', 'Quá trình đặt lại mật khẩu được bảo mật và mã hóa.'); ?>
                    </small>
                </div>
                
                <!-- Password Requirements -->
                <div class="password-requirements">
                    <h6><i class="fas fa-shield-alt me-2"></i><?php echo __('password_requirements', 'Yêu cầu mật khẩu'); ?></h6>
                    <ul>
                        <li><?php echo __('min_6_characters', 'Ít nhất 6 ký tự'); ?></li>
                        <li><?php echo __('recommend_mix', 'Nên kết hợp chữ, số và ký tự đặc biệt'); ?></li>
                        <li><?php echo __('avoid_personal_info', 'Tránh thông tin cá nhân dễ đoán'); ?></li>
                    </ul>
                </div>
                
                <!-- Reset Form -->
                <form method="POST" id="resetPasswordForm">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    
                    <div class="form-outline">
                        <input type="password" id="new_password" name="new_password" class="form-control" 
                               minlength="6" required>
                        <label class="form-label" for="new_password">
                            <i class="fas fa-lock me-2"></i><?php echo __('new_password', 'Mật khẩu mới'); ?>
                        </label>
                        <div class="password-strength" id="passwordStrength"></div>
                        <div class="form-text" id="strengthText"></div>
                    </div>
                    
                    <div class="form-outline">
                        <input type="password" id="confirm_password" name="confirm_password" class="form-control" 
                               minlength="6" required>
                        <label class="form-label" for="confirm_password">
                            <i class="fas fa-lock me-2"></i><?php echo __('confirm_password', 'Xác nhận mật khẩu'); ?>
                        </label>
                        <div class="form-text" id="matchText"></div>
                    </div>
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary btn-reset" id="resetBtn">
                            <i class="fas fa-key me-2"></i>
                            <?php echo __('reset_password', 'Đặt lại mật khẩu'); ?>
                        </button>
                    </div>
                </form>
            <?php endif; ?>
            
            <!-- Back to Login -->
            <div class="text-center mt-4">
                <p class="mb-0">
                    <a href="login.php" class="text-decoration-none">
                        <i class="fas fa-arrow-left me-1"></i><?php echo __('back_to_login', 'Quay về đăng nhập'); ?>
                    </a>
                </p>
            </div>
        </div>
    </div>
    
    <!-- MDBootstrap JS -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/mdb-ui-kit/6.4.2/mdb.min.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const newPasswordInput = document.getElementById('new_password');
            const confirmPasswordInput = document.getElementById('confirm_password');
            const passwordStrength = document.getElementById('passwordStrength');
            const strengthText = document.getElementById('strengthText');
            const matchText = document.getElementById('matchText');
            const resetBtn = document.getElementById('resetBtn');
            
            if (newPasswordInput) {
                // Password strength checker
                newPasswordInput.addEventListener('input', function() {
                    const password = this.value;
                    const strength = checkPasswordStrength(password);
                    
                    passwordStrength.className = 'password-strength strength-' + strength.level;
                    strengthText.textContent = strength.text;
                    strengthText.className = 'form-text text-' + (strength.level === 'weak' ? 'danger' : (strength.level === 'medium' ? 'warning' : 'success'));
                    
                    validateForm();
                });
                
                // Password confirmation checker
                confirmPasswordInput.addEventListener('input', function() {
                    const password = newPasswordInput.value;
                    const confirm = this.value;
                    
                    if (confirm.length > 0) {
                        if (password === confirm) {
                            matchText.textContent = '<?php echo __('passwords_match', 'Mật khẩu khớp'); ?>';
                            matchText.className = 'form-text text-success';
                        } else {
                            matchText.textContent = '<?php echo __('passwords_not_match', 'Mật khẩu không khớp'); ?>';
                            matchText.className = 'form-text text-danger';
                        }
                    } else {
                        matchText.textContent = '';
                    }
                    
                    validateForm();
                });
                
                function checkPasswordStrength(password) {
                    if (password.length < 6) {
                        return { level: 'weak', text: '<?php echo __('password_too_short', 'Mật khẩu quá ngắn'); ?>' };
                    }
                    
                    let score = 0;
                    if (password.length >= 8) score++;
                    if (/[a-z]/.test(password)) score++;
                    if (/[A-Z]/.test(password)) score++;
                    if (/[0-9]/.test(password)) score++;
                    if (/[^A-Za-z0-9]/.test(password)) score++;
                    
                    if (score < 3) {
                        return { level: 'weak', text: '<?php echo __('password_weak', 'Mật khẩu yếu'); ?>' };
                    } else if (score < 4) {
                        return { level: 'medium', text: '<?php echo __('password_medium', 'Mật khẩu trung bình'); ?>' };
                    } else {
                        return { level: 'strong', text: '<?php echo __('password_strong', 'Mật khẩu mạnh'); ?>' };
                    }
                }
                
                function validateForm() {
                    const password = newPasswordInput.value;
                    const confirm = confirmPasswordInput.value;
                    const isValid = password.length >= 6 && password === confirm;
                    
                    if (resetBtn) {
                        resetBtn.disabled = !isValid;
                    }
                }
                
                // Form submission
                document.getElementById('resetPasswordForm').addEventListener('submit', function(e) {
                    const password = newPasswordInput.value;
                    const confirm = confirmPasswordInput.value;
                    
                    if (password !== confirm) {
                        e.preventDefault();
                        alert('<?php echo __('passwords_not_match', 'Mật khẩu không khớp'); ?>');
                        return;
                    }
                    
                    if (password.length < 6) {
                        e.preventDefault();
                        alert('<?php echo __('password_min_length', 'Mật khẩu phải có ít nhất 6 ký tự'); ?>');
                        return;
                    }
                    
                    // Show loading state
                    const originalText = resetBtn.innerHTML;
                    resetBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i><?php echo __('resetting', 'Đang đặt lại...'); ?>';
                    resetBtn.disabled = true;
                });
            }
        });
    </script>
</body>
</html>
