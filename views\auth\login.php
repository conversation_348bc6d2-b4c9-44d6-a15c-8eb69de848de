<?php
/**
 * Login Page
 * 
 * Handles user authentication for the restaurant management system
 */

require_once '../../config/config.php';
require_once '../../config/database.php';
require_once '../../includes/functions.php';
require_once '../../models/User.php';

// Redirect if already logged in
if (isLoggedIn()) {
    header('Location: ../../index.php');
    exit();
}

$error = '';
$success = '';

// Handle login form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
        $error = 'Invalid security token. Please try again.';
    } else {
        $username = sanitizeInput($_POST['username'] ?? '');
        $password = $_POST['password'] ?? '';
        
        if (empty($username) || empty($password)) {
            $error = __('required_field_both', 'Vui lòng nhập tên đăng nhập và mật khẩu.');
        } else {
            $userModel = new User();
            $user = $userModel->authenticate($username, $password);

            if ($user) {
                // Set session variables
                $_SESSION['user_id'] = $user['user_id'];
                $_SESSION['username'] = $user['username'];
                $_SESSION['full_name'] = $user['full_name'];
                $_SESSION['email'] = $user['email'];
                $_SESSION['role'] = $user['role_name'];
                $_SESSION['last_activity'] = time();

                // Log activity
                logActivity("Người dùng đăng nhập: {$user['username']}", $user['user_id']);

                // Redirect to appropriate dashboard
                if ($user['role_name'] === 'admin') {
                    header('Location: ../admin/dashboard.php');
                } else {
                    header('Location: ../staff/dashboard.php');
                }
                exit();
            } else {
                $error = __('invalid_username_password', 'Tên đăng nhập hoặc mật khẩu không đúng.');
                logActivity("Đăng nhập thất bại cho tên đăng nhập: $username");
            }
        }
    }
}

// Handle logout message
if (isset($_GET['logout'])) {
    $success = __('logout_success', 'Bạn đã đăng xuất thành công.');
}

// Handle timeout message
if (isset($_GET['timeout'])) {
    $error = __('session_expired', 'Phiên làm việc đã hết hạn. Vui lòng đăng nhập lại.');
}

// Handle unauthorized access
if (isset($_GET['unauthorized'])) {
    $error = __('unauthorized_access', 'Bạn không có quyền truy cập trang đó.');
}

$pageTitle = __('login') . ' - ' . APP_NAME;
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?></title>
    
    <!-- MDBootstrap CSS -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/mdb-ui-kit/6.4.2/mdb.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .login-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .login-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0;
            padding: 2rem;
            text-align: center;
        }
        .login-body {
            padding: 2rem;
        }
        .form-outline {
            margin-bottom: 1.5rem;
        }
        .btn-login {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        .demo-credentials {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1rem;
            margin-top: 1rem;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-4">
                <div class="card login-card">
                    <div class="login-header">
                        <i class="fas fa-utensils fa-3x mb-3"></i>
                        <h3 class="mb-0"><?php echo APP_NAME; ?></h3>
                        <p class="mb-0"><?php echo __('login_subtitle', 'Vui lòng đăng nhập để tiếp tục'); ?></p>
                    </div>
                    
                    <div class="login-body">
                        <?php if ($error): ?>
                            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <?php echo $error; ?>
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($success): ?>
                            <div class="alert alert-success alert-dismissible fade show" role="alert">
                                <i class="fas fa-check-circle me-2"></i>
                                <?php echo $success; ?>
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        <?php endif; ?>
                        
                        <form method="POST" action="">
                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                            
                            <div class="form-outline">
                                <input type="text" id="username" name="username" class="form-control" 
                                       value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>" required>
                                <label class="form-label" for="username">
                                    <i class="fas fa-user me-2"></i><?php echo __('login_username_label', 'Tên đăng nhập hoặc Email'); ?>
                                </label>
                            </div>
                            
                            <div class="form-outline">
                                <input type="password" id="password" name="password" class="form-control" required>
                                <label class="form-label" for="password">
                                    <i class="fas fa-lock me-2"></i><?php echo __('password', 'Mật khẩu'); ?>
                                </label>
                            </div>
                            
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary btn-login">
                                    <i class="fas fa-sign-in-alt me-2"></i><?php echo __('login_button', 'Đăng nhập'); ?>
                                </button>
                            </div>
                        </form>
                        
                        <div class="text-center mt-3">
                            <a href="forgot-password.php" class="text-decoration-none">
                                <i class="fas fa-question-circle me-1"></i><?php echo __('login_forgot_password', 'Quên mật khẩu?'); ?>
                            </a>
                        </div>

                        <!-- Demo Credentials -->
                        <div class="demo-credentials">
                            <h6 class="mb-2"><i class="fas fa-info-circle me-2"></i><?php echo __('login_demo_credentials', 'Tài khoản demo:'); ?></h6>
                            <div class="row">
                                <div class="col-6">
                                    <strong><?php echo __('admin', 'Quản trị viên'); ?>:</strong><br>
                                    <?php echo __('username', 'Tên đăng nhập'); ?>: admin<br>
                                    <?php echo __('password', 'Mật khẩu'); ?>: admin123
                                </div>
                                <div class="col-6">
                                    <strong><?php echo __('staff', 'Nhân viên'); ?>:</strong><br>
                                    <?php echo __('username', 'Tên đăng nhập'); ?>: staff<br>
                                    <?php echo __('password', 'Mật khẩu'); ?>: staff123
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- MDBootstrap JS -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/mdb-ui-kit/6.4.2/mdb.min.js"></script>
    
    <script>
        // Initialize MDBootstrap components
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize form validation
            const forms = document.querySelectorAll('form');
            forms.forEach(form => {
                form.addEventListener('submit', function(e) {
                    const username = document.getElementById('username').value.trim();
                    const password = document.getElementById('password').value;
                    
                    if (!username || !password) {
                        e.preventDefault();
                        alert('Please fill in all required fields.');
                        return false;
                    }
                });
            });
            
            // Auto-dismiss alerts after 5 seconds
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                setTimeout(() => {
                    const bsAlert = new mdb.Alert(alert);
                    bsAlert.close();
                }, 5000);
            });
        });
    </script>
</body>
</html>
