<?php
/**
 * Order Details Page
 *
 * Detailed order view with item modifications, status tracking,
 * and order management functionality.
 */

require_once '../../config/config.php';
require_once '../../config/database.php';
require_once '../../includes/functions.php';
require_once '../../models/Order.php';
require_once '../../models/FoodItem.php';

// Check staff access
requireLogin();

// Initialize models
$orderModel = new Order();
$foodItemModel = new FoodItem();

// Get order ID from URL
$orderId = (int)($_GET['id'] ?? 0);

if ($orderId <= 0) {
    setFlashMessage('error', __('invalid_order_id', 'ID đơn hàng không hợp lệ.'));
    header('Location: orders.php');
    exit();
}

// Get order details
$order = $orderModel->getOrderById($orderId);
if (!$order) {
    setFlashMessage('error', __('order_not_found', 'Không tìm thấy đơn hàng.'));
    header('Location: orders.php');
    exit();
}

// Get order items
$orderItems = $orderModel->getOrderItems($orderId);

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
        setFlashMessage('error', __('invalid_csrf_token', 'Token bảo mật không hợp lệ. Vui lòng thử lại.'));
    } else {
        $action = $_POST['action'] ?? '';

        switch ($action) {
            case 'update_status':
                $newStatus = sanitizeInput($_POST['status'] ?? '');
                $validStatuses = ['pending', 'processing', 'completed', 'cancelled'];

                if (in_array($newStatus, $validStatuses)) {
                    if ($orderModel->updateOrderStatus($orderId, $newStatus)) {
                        setFlashMessage('success', __('order_status_updated', 'Cập nhật trạng thái đơn hàng thành công.'));
                        logActivity("Cập nhật trạng thái đơn hàng #$orderId thành $newStatus", $_SESSION['user_id']);

                        // Update table status based on order status
                        if ($newStatus === 'completed' || $newStatus === 'cancelled') {
                            $tableModel = new Table();
                            $tableModel->updateTableStatus($order['table_id'], 'available');
                        }
                    } else {
                        setFlashMessage('error', __('order_status_update_failed', 'Cập nhật trạng thái thất bại.'));
                    }
                } else {
                    setFlashMessage('error', __('invalid_status', 'Trạng thái không hợp lệ.'));
                }
                break;

            case 'add_item':
                $foodId = (int)($_POST['food_id'] ?? 0);
                $quantity = (int)($_POST['quantity'] ?? 1);

                if ($foodId > 0 && $quantity > 0) {
                    $foodItem = $foodItemModel->getFoodItemById($foodId);
                    if ($foodItem && $foodItem['status']) {
                        $itemData = [
                            'order_id' => $orderId,
                            'food_id' => $foodId,
                            'quantity' => $quantity,
                            'unit_price' => $foodItem['price']
                        ];

                        if ($orderModel->addOrderItem($itemData)) {
                            setFlashMessage('success', __('item_added_success', 'Thêm món ăn thành công.'));
                            logActivity("Thêm món {$foodItem['food_name']} vào đơn hàng #$orderId", $_SESSION['user_id']);
                        } else {
                            setFlashMessage('error', __('item_add_failed', 'Thêm món ăn thất bại.'));
                        }
                    } else {
                        setFlashMessage('error', __('food_item_not_available', 'Món ăn không khả dụng.'));
                    }
                } else {
                    setFlashMessage('error', __('invalid_item_data', 'Dữ liệu món ăn không hợp lệ.'));
                }
                break;

            case 'update_item':
                $itemId = (int)($_POST['item_id'] ?? 0);
                $quantity = (int)($_POST['quantity'] ?? 1);

                if ($itemId > 0 && $quantity > 0) {
                    if ($orderModel->updateOrderItemQuantity($itemId, $quantity)) {
                        setFlashMessage('success', __('item_updated_success', 'Cập nhật món ăn thành công.'));
                        logActivity("Cập nhật số lượng món trong đơn hàng #$orderId", $_SESSION['user_id']);
                    } else {
                        setFlashMessage('error', __('item_update_failed', 'Cập nhật món ăn thất bại.'));
                    }
                } else {
                    setFlashMessage('error', __('invalid_quantity', 'Số lượng không hợp lệ.'));
                }
                break;

            case 'remove_item':
                $itemId = (int)($_POST['item_id'] ?? 0);

                if ($itemId > 0) {
                    if ($orderModel->removeOrderItem($itemId)) {
                        setFlashMessage('success', __('item_removed_success', 'Xóa món ăn thành công.'));
                        logActivity("Xóa món khỏi đơn hàng #$orderId", $_SESSION['user_id']);
                    } else {
                        setFlashMessage('error', __('item_remove_failed', 'Xóa món ăn thất bại.'));
                    }
                } else {
                    setFlashMessage('error', __('invalid_item_id', 'ID món ăn không hợp lệ.'));
                }
                break;
        }

        // Redirect to refresh data
        header("Location: order-details.php?id=$orderId");
        exit();
    }
}

// Refresh order data after potential updates
$order = $orderModel->getOrderById($orderId);
$orderItems = $orderModel->getOrderItems($orderId);

// Get available food items for adding
$availableFoodItems = $foodItemModel->getAvailableFoodItems();

$pageTitle = __('order_details', 'Chi tiết đơn hàng') . ' #' . $orderId . ' - ' . APP_NAME;
?>
<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?></title>

    <!-- MDBootstrap CSS -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/mdb-ui-kit/6.4.2/mdb.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="../../assets/css/custom.css" rel="stylesheet">

    <style>
        .order-status {
            font-size: 1.1rem;
            font-weight: bold;
            padding: 8px 16px;
            border-radius: 20px;
        }
        .status-pending { background: #fff3cd; color: #856404; }
        .status-processing { background: #d1ecf1; color: #0c5460; }
        .status-completed { background: #d4edda; color: #155724; }
        .status-cancelled { background: #f8d7da; color: #721c24; }

        .order-timeline {
            position: relative;
            padding-left: 30px;
        }
        .order-timeline::before {
            content: '';
            position: absolute;
            left: 15px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #dee2e6;
        }
        .timeline-item {
            position: relative;
            margin-bottom: 20px;
        }
        .timeline-item::before {
            content: '';
            position: absolute;
            left: -23px;
            top: 5px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #007bff;
        }
        .timeline-item.completed::before {
            background: #28a745;
        }
        .timeline-item.current::before {
            background: #ffc107;
            box-shadow: 0 0 0 4px rgba(255, 193, 7, 0.3);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="dashboard.php">
                <i class="fas fa-utensils me-2"></i><?php echo APP_NAME; ?>
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-1"></i><?php echo __('dashboard', 'Bảng điều khiển'); ?>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="tables.php">
                            <i class="fas fa-chair me-1"></i><?php echo __('tables', 'Bàn'); ?>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="orders.php">
                            <i class="fas fa-receipt me-1"></i><?php echo __('orders', 'Đơn hàng'); ?>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="new-order.php">
                            <i class="fas fa-plus me-1"></i><?php echo __('new_order', 'Đơn hàng mới'); ?>
                        </a>
                    </li>
                </ul>

                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle me-1"></i>
                            <?php echo htmlspecialchars($_SESSION['full_name']); ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="profile.php">
                                <i class="fas fa-user me-2"></i><?php echo __('profile', 'Hồ sơ'); ?>
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../auth/logout.php">
                                <i class="fas fa-sign-out-alt me-2"></i><?php echo __('logout', 'Đăng xuất'); ?>
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-fluid mt-4">
        <div class="row">
            <!-- Order Information -->
            <div class="col-lg-8">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2>
                        <i class="fas fa-receipt me-2"></i>
                        <?php echo __('order_details', 'Chi tiết đơn hàng'); ?> #<?php echo $orderId; ?>
                    </h2>
                    <div>
                        <a href="orders.php" class="btn btn-outline-secondary me-2">
                            <i class="fas fa-arrow-left me-2"></i><?php echo __('back_to_orders', 'Quay về đơn hàng'); ?>
                        </a>
                        <?php if ($order['status'] === 'completed'): ?>
                            <a href="payment.php?order_id=<?php echo $orderId; ?>" class="btn btn-success">
                                <i class="fas fa-credit-card me-2"></i><?php echo __('process_payment', 'Xử lý thanh toán'); ?>
                            </a>
                        <?php endif; ?>
                    </div>
                </div>

                <?php displayFlashMessage(); ?>

                <!-- Order Header Information -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i><?php echo __('order_information', 'Thông tin đơn hàng'); ?>
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong><?php echo __('table', 'Bàn'); ?>:</strong>
                                   <?php echo __('table', 'Bàn'); ?> <?php echo htmlspecialchars($order['table_number']); ?>
                                   (<?php echo $order['capacity']; ?> <?php echo __('people', 'người'); ?>)
                                </p>
                                <p><strong><?php echo __('staff', 'Nhân viên'); ?>:</strong>
                                   <?php echo htmlspecialchars($order['staff_name']); ?>
                                </p>
                                <p><strong><?php echo __('created_at', 'Ngày tạo'); ?>:</strong>
                                   <?php echo formatVietnameseDate($order['created_at']); ?>
                                </p>
                            </div>
                            <div class="col-md-6">
                                <?php if ($order['customer_name']): ?>
                                    <p><strong><?php echo __('customer_name', 'Tên khách hàng'); ?>:</strong>
                                       <?php echo htmlspecialchars($order['customer_name']); ?>
                                    </p>
                                <?php endif; ?>
                                <?php if ($order['customer_phone']): ?>
                                    <p><strong><?php echo __('customer_phone', 'Số điện thoại'); ?>:</strong>
                                       <?php echo htmlspecialchars($order['customer_phone']); ?>
                                    </p>
                                <?php endif; ?>
                                <p><strong><?php echo __('status', 'Trạng thái'); ?>:</strong>
                                   <span class="order-status status-<?php echo $order['status']; ?>">
                                       <?php echo __('order_status_' . $order['status'], ucfirst($order['status'])); ?>
                                   </span>
                                </p>
                            </div>
                        </div>
                        <?php if ($order['notes']): ?>
                            <div class="mt-3">
                                <strong><?php echo __('order_notes', 'Ghi chú đơn hàng'); ?>:</strong>
                                <p class="mt-2 p-3 bg-light rounded"><?php echo nl2br(htmlspecialchars($order['notes'])); ?></p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Order Items -->
                <div class="card mb-4">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-utensils me-2"></i><?php echo __('order_items', 'Món đã chọn'); ?>
                        </h5>
                        <?php if ($order['status'] === 'pending' || $order['status'] === 'processing'): ?>
                            <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#addItemModal">
                                <i class="fas fa-plus me-1"></i><?php echo __('add_item', 'Thêm món'); ?>
                            </button>
                        <?php endif; ?>
                    </div>
                    <div class="card-body">
                        <?php if (empty($orderItems)): ?>
                            <p class="text-muted text-center py-4">
                                <?php echo __('no_items_in_order', 'Không có món ăn nào trong đơn hàng.'); ?>
                            </p>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th><?php echo __('food_name', 'Tên món ăn'); ?></th>
                                            <th><?php echo __('unit_price', 'Đơn giá'); ?></th>
                                            <th><?php echo __('quantity', 'Số lượng'); ?></th>
                                            <th><?php echo __('total_price', 'Thành tiền'); ?></th>
                                            <?php if ($order['status'] === 'pending' || $order['status'] === 'processing'): ?>
                                                <th><?php echo __('actions', 'Thao tác'); ?></th>
                                            <?php endif; ?>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php
                                        $orderTotal = 0;
                                        foreach ($orderItems as $item):
                                            $itemTotal = $item['unit_price'] * $item['quantity'];
                                            $orderTotal += $itemTotal;
                                        ?>
                                            <tr>
                                                <td>
                                                    <strong><?php echo htmlspecialchars($item['food_name']); ?></strong>
                                                    <?php if ($item['description']): ?>
                                                        <br><small class="text-muted"><?php echo truncateText(htmlspecialchars($item['description']), 50); ?></small>
                                                    <?php endif; ?>
                                                </td>
                                                <td><?php echo formatCurrency($item['unit_price']); ?></td>
                                                <td>
                                                    <?php if ($order['status'] === 'pending' || $order['status'] === 'processing'): ?>
                                                        <form method="POST" style="display: inline-block;">
                                                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                                            <input type="hidden" name="action" value="update_item">
                                                            <input type="hidden" name="item_id" value="<?php echo $item['order_item_id']; ?>">
                                                            <div class="input-group" style="width: 120px;">
                                                                <input type="number" name="quantity" class="form-control form-control-sm"
                                                                       value="<?php echo $item['quantity']; ?>" min="1" max="99">
                                                                <button type="submit" class="btn btn-sm btn-outline-primary">
                                                                    <i class="fas fa-check"></i>
                                                                </button>
                                                            </div>
                                                        </form>
                                                    <?php else: ?>
                                                        <?php echo $item['quantity']; ?>
                                                    <?php endif; ?>
                                                </td>
                                                <td><strong><?php echo formatCurrency($itemTotal); ?></strong></td>
                                                <?php if ($order['status'] === 'pending' || $order['status'] === 'processing'): ?>
                                                    <td>
                                                        <form method="POST" style="display: inline-block;"
                                                              onsubmit="return confirm('<?php echo __('confirm_remove_item', 'Bạn có chắc chắn muốn xóa món này?'); ?>')">
                                                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                                            <input type="hidden" name="action" value="remove_item">
                                                            <input type="hidden" name="item_id" value="<?php echo $item['order_item_id']; ?>">
                                                            <button type="submit" class="btn btn-sm btn-outline-danger">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        </form>
                                                    </td>
                                                <?php endif; ?>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                    <tfoot>
                                        <tr class="table-active">
                                            <th colspan="<?php echo ($order['status'] === 'pending' || $order['status'] === 'processing') ? '4' : '3'; ?>">
                                                <?php echo __('total_amount', 'Tổng tiền'); ?>:
                                            </th>
                                            <th class="text-primary">
                                                <?php echo formatCurrency($orderTotal); ?>
                                            </th>
                                            <?php if ($order['status'] === 'pending' || $order['status'] === 'processing'): ?>
                                                <th></th>
                                            <?php endif; ?>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Order Status & Actions -->
            <div class="col-lg-4">
                <!-- Status Management -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-tasks me-2"></i><?php echo __('order_status_management', 'Quản lý trạng thái'); ?>
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                            <input type="hidden" name="action" value="update_status">

                            <div class="mb-3">
                                <label for="status" class="form-label"><?php echo __('current_status', 'Trạng thái hiện tại'); ?></label>
                                <select id="status" name="status" class="form-select">
                                    <option value="pending" <?php echo $order['status'] === 'pending' ? 'selected' : ''; ?>>
                                        <?php echo __('order_status_pending', 'Chờ xử lý'); ?>
                                    </option>
                                    <option value="processing" <?php echo $order['status'] === 'processing' ? 'selected' : ''; ?>>
                                        <?php echo __('order_status_processing', 'Đang xử lý'); ?>
                                    </option>
                                    <option value="completed" <?php echo $order['status'] === 'completed' ? 'selected' : ''; ?>>
                                        <?php echo __('order_status_completed', 'Hoàn thành'); ?>
                                    </option>
                                    <option value="cancelled" <?php echo $order['status'] === 'cancelled' ? 'selected' : ''; ?>>
                                        <?php echo __('order_status_cancelled', 'Đã hủy'); ?>
                                    </option>
                                </select>
                            </div>

                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-save me-2"></i><?php echo __('update_status', 'Cập nhật trạng thái'); ?>
                            </button>
                        </form>
                    </div>
                </div>

                <!-- Order Timeline -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-history me-2"></i><?php echo __('order_timeline', 'Tiến trình đơn hàng'); ?>
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="order-timeline">
                            <div class="timeline-item <?php echo in_array($order['status'], ['pending', 'processing', 'completed', 'cancelled']) ? 'completed' : ''; ?>">
                                <strong><?php echo __('order_created', 'Đơn hàng được tạo'); ?></strong>
                                <br><small class="text-muted"><?php echo formatVietnameseDate($order['created_at']); ?></small>
                            </div>

                            <div class="timeline-item <?php echo $order['status'] === 'processing' ? 'current' : (in_array($order['status'], ['completed', 'cancelled']) ? 'completed' : ''); ?>">
                                <strong><?php echo __('order_processing', 'Đang xử lý'); ?></strong>
                                <?php if ($order['status'] === 'processing'): ?>
                                    <br><small class="text-muted"><?php echo __('current_status', 'Trạng thái hiện tại'); ?></small>
                                <?php endif; ?>
                            </div>

                            <?php if ($order['status'] === 'completed'): ?>
                                <div class="timeline-item completed">
                                    <strong><?php echo __('order_completed', 'Hoàn thành'); ?></strong>
                                    <br><small class="text-muted"><?php echo formatVietnameseDate($order['updated_at']); ?></small>
                                </div>
                            <?php elseif ($order['status'] === 'cancelled'): ?>
                                <div class="timeline-item" style="color: #dc3545;">
                                    <strong><?php echo __('order_cancelled', 'Đã hủy'); ?></strong>
                                    <br><small class="text-muted"><?php echo formatVietnameseDate($order['updated_at']); ?></small>
                                </div>
                            <?php else: ?>
                                <div class="timeline-item">
                                    <strong><?php echo __('order_completion', 'Hoàn thành đơn hàng'); ?></strong>
                                    <br><small class="text-muted"><?php echo __('pending_completion', 'Chờ hoàn thành'); ?></small>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Item Modal -->
    <?php if ($order['status'] === 'pending' || $order['status'] === 'processing'): ?>
    <div class="modal fade" id="addItemModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-plus me-2"></i><?php echo __('add_item_to_order', 'Thêm món vào đơn hàng'); ?>
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                        <input type="hidden" name="action" value="add_item">

                        <div class="row">
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label for="food_id" class="form-label"><?php echo __('select_food_item', 'Chọn món ăn'); ?> *</label>
                                    <select id="food_id" name="food_id" class="form-select" required>
                                        <option value=""><?php echo __('select_food_item', 'Chọn món ăn'); ?></option>
                                        <?php foreach ($availableFoodItems as $item): ?>
                                            <option value="<?php echo $item['food_id']; ?>" data-price="<?php echo $item['price']; ?>">
                                                <?php echo htmlspecialchars($item['food_name']); ?> - <?php echo formatCurrency($item['price']); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-outline mb-3">
                                    <input type="number" id="quantity" name="quantity" class="form-control" value="1" min="1" max="99" required>
                                    <label class="form-label" for="quantity"><?php echo __('quantity', 'Số lượng'); ?> *</label>
                                </div>
                            </div>
                        </div>

                        <div id="itemPreview" class="alert alert-info" style="display: none;">
                            <strong><?php echo __('item_preview', 'Xem trước'); ?>:</strong>
                            <span id="previewText"></span>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?php echo __('cancel', 'Hủy'); ?></button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i><?php echo __('add_item', 'Thêm món'); ?>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- MDBootstrap JS -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/mdb-ui-kit/6.4.2/mdb.min.js"></script>
    <!-- Custom JS -->
    <script src="../../assets/js/app.js"></script>

    <script>
        // Add item preview functionality
        document.addEventListener('DOMContentLoaded', function() {
            const foodSelect = document.getElementById('food_id');
            const quantityInput = document.getElementById('quantity');
            const itemPreview = document.getElementById('itemPreview');
            const previewText = document.getElementById('previewText');

            function updatePreview() {
                const selectedOption = foodSelect.options[foodSelect.selectedIndex];
                const quantity = parseInt(quantityInput.value) || 1;

                if (selectedOption.value) {
                    const foodName = selectedOption.text.split(' - ')[0];
                    const price = parseFloat(selectedOption.dataset.price);
                    const total = price * quantity;

                    previewText.textContent = `${foodName} × ${quantity} = ${formatCurrency(total)}`;
                    itemPreview.style.display = 'block';
                } else {
                    itemPreview.style.display = 'none';
                }
            }

            if (foodSelect && quantityInput) {
                foodSelect.addEventListener('change', updatePreview);
                quantityInput.addEventListener('input', updatePreview);
            }

            // Format currency function
            function formatCurrency(amount) {
                return new Intl.NumberFormat('vi-VN').format(amount) + ' ₫';
            }

            // Auto-refresh order status every 30 seconds
            setInterval(function() {
                // Only refresh if order is not completed or cancelled
                const currentStatus = '<?php echo $order['status']; ?>';
                if (currentStatus === 'pending' || currentStatus === 'processing') {
                    // Add a subtle indicator that data is being refreshed
                    const refreshIndicator = document.createElement('div');
                    refreshIndicator.className = 'position-fixed top-0 end-0 m-3 alert alert-info alert-dismissible fade show';
                    refreshIndicator.style.zIndex = '9999';
                    refreshIndicator.innerHTML = `
                        <i class="fas fa-sync-alt fa-spin me-2"></i><?php echo __('refreshing_data', 'Đang cập nhật dữ liệu...'); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    `;
                    document.body.appendChild(refreshIndicator);

                    // Auto-remove after 2 seconds
                    setTimeout(() => {
                        if (refreshIndicator.parentNode) {
                            refreshIndicator.remove();
                        }
                    }, 2000);

                    // Refresh the page to get updated data
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000);
                }
            }, 30000); // 30 seconds
        });

        // Confirmation for status changes
        document.querySelector('form[action="update_status"]')?.addEventListener('submit', function(e) {
            const newStatus = document.getElementById('status').value;
            const currentStatus = '<?php echo $order['status']; ?>';

            if (newStatus !== currentStatus) {
                const statusNames = {
                    'pending': '<?php echo __('order_status_pending', 'Chờ xử lý'); ?>',
                    'processing': '<?php echo __('order_status_processing', 'Đang xử lý'); ?>',
                    'completed': '<?php echo __('order_status_completed', 'Hoàn thành'); ?>',
                    'cancelled': '<?php echo __('order_status_cancelled', 'Đã hủy'); ?>'
                };

                const confirmMessage = `<?php echo __('confirm_status_change', 'Bạn có chắc chắn muốn thay đổi trạng thái đơn hàng thành'); ?> "${statusNames[newStatus]}"?`;

                if (!confirm(confirmMessage)) {
                    e.preventDefault();
                }
            }
        });
    </script>
</body>
</html>