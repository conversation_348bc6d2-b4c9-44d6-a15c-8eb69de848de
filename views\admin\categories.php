<?php
/**
 * Categories Management Page
 * 
 * Admin interface for managing food categories including CRUD operations,
 * search functionality, and category statistics.
 */

require_once '../../config/config.php';
require_once '../../config/database.php';
require_once '../../includes/functions.php';
require_once '../../models/Category.php';

// Check admin access
requireAdmin();

// Initialize category model
$categoryModel = new Category();

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
        setFlashMessage('error', 'Invalid security token. Please try again.');
    } else {
        $action = $_POST['action'] ?? '';
        
        switch ($action) {
            case 'create':
                $categoryData = [
                    'category_name' => sanitizeInput($_POST['category_name'] ?? ''),
                    'description' => sanitizeInput($_POST['description'] ?? ''),
                    'status' => (int)($_POST['status'] ?? 1)
                ];
                
                if (empty($categoryData['category_name'])) {
                    setFlashMessage('error', __('category_name_required', 'Tên danh mục là bắt buộc.'));
                } else {
                    $categoryId = $categoryModel->createCategory($categoryData);
                    if ($categoryId) {
                        setFlashMessage('success', __('category_created_success', 'Tạo danh mục thành công.'));
                        logActivity("Tạo danh mục: {$categoryData['category_name']}", $_SESSION['user_id']);
                    } else {
                        setFlashMessage('error', __('category_create_failed', 'Tạo danh mục thất bại. Tên danh mục có thể đã tồn tại.'));
                    }
                }
                break;
                
            case 'update':
                $categoryId = (int)($_POST['category_id'] ?? 0);
                $categoryData = [
                    'category_name' => sanitizeInput($_POST['category_name'] ?? ''),
                    'description' => sanitizeInput($_POST['description'] ?? ''),
                    'status' => (int)($_POST['status'] ?? 1)
                ];
                
                if (empty($categoryData['category_name'])) {
                    setFlashMessage('error', __('category_name_required', 'Tên danh mục là bắt buộc.'));
                } else {
                    if ($categoryModel->updateCategory($categoryId, $categoryData)) {
                        setFlashMessage('success', __('category_updated_success', 'Cập nhật danh mục thành công.'));
                        logActivity("Cập nhật danh mục ID: $categoryId", $_SESSION['user_id']);
                    } else {
                        setFlashMessage('error', __('category_update_failed', 'Cập nhật danh mục thất bại. Tên danh mục có thể đã tồn tại.'));
                    }
                }
                break;
                
            case 'delete':
                $categoryId = (int)($_POST['category_id'] ?? 0);
                if ($categoryModel->deleteCategory($categoryId)) {
                    setFlashMessage('success', __('category_deleted_success', 'Xóa danh mục thành công.'));
                    logActivity("Xóa danh mục ID: $categoryId", $_SESSION['user_id']);
                } else {
                    setFlashMessage('error', __('category_delete_failed', 'Xóa danh mục thất bại. Danh mục có thể có món ăn liên quan.'));
                }
                break;
        }
        
        // Redirect to prevent form resubmission
        header('Location: categories.php');
        exit();
    }
}

// Handle GET requests for editing
$editCategory = null;
if (isset($_GET['edit'])) {
    $editId = (int)$_GET['edit'];
    $editCategory = $categoryModel->getCategoryById($editId);
}

// Get categories with pagination and search
$page = (int)($_GET['page'] ?? 1);
$search = sanitizeInput($_GET['search'] ?? '');

if ($search) {
    $categoriesData = $categoryModel->searchCategories($search, $page);
} else {
    $categoriesData = $categoryModel->getAllCategories($page);
}

$pageTitle = __('categories_management', 'Quản lý danh mục') . ' - ' . APP_NAME;
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?></title>
    
    <!-- MDBootstrap CSS -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/mdb-ui-kit/6.4.2/mdb.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        .sidebar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            position: fixed;
            top: 0;
            left: 0;
            width: 250px;
            z-index: 1000;
        }
        .main-content {
            margin-left: 250px;
            padding: 20px;
        }
        .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 0.75rem 1rem;
            border-radius: 8px;
            margin: 0.25rem 0;
            transition: all 0.3s ease;
        }
        .nav-link:hover, .nav-link.active {
            background: rgba(255, 255, 255, 0.1);
            color: white;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        .btn-action {
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <nav class="sidebar">
        <div class="p-3">
            <div class="text-center mb-4">
                <i class="fas fa-utensils fa-2x text-white mb-2"></i>
                <h5 class="text-white mb-0"><?php echo APP_NAME; ?></h5>
                <small class="text-white-50">Admin Panel</small>
            </div>
            
            <div class="mb-3">
                <small class="text-white-50 text-uppercase">Welcome</small>
                <div class="text-white">
                    <i class="fas fa-user-shield me-2"></i>
                    <?php echo htmlspecialchars($_SESSION['full_name']); ?>
                </div>
            </div>
            
            <hr class="text-white-50">
            
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link" href="dashboard.php">
                        <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link active" href="categories.php">
                        <i class="fas fa-tags me-2"></i>Categories
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="food-items.php">
                        <i class="fas fa-hamburger me-2"></i>Food Items
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="tables.php">
                        <i class="fas fa-chair me-2"></i>Tables
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="users.php">
                        <i class="fas fa-users me-2"></i>Users
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="orders.php">
                        <i class="fas fa-receipt me-2"></i>Orders
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="reports.php">
                        <i class="fas fa-chart-bar me-2"></i>Reports
                    </a>
                </li>
            </ul>
            
            <hr class="text-white-50">
            
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link" href="../auth/logout.php">
                        <i class="fas fa-sign-out-alt me-2"></i>Logout
                    </a>
                </li>
            </ul>
        </div>
    </nav>
    
    <!-- Main Content -->
    <div class="main-content">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2><i class="fas fa-tags me-2"></i><?php echo __('categories_management', 'Quản lý danh mục'); ?></h2>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#categoryModal">
                <i class="fas fa-plus me-2"></i><?php echo __('add_category', 'Thêm danh mục'); ?>
            </button>
        </div>
        
        <?php displayFlashMessage(); ?>
        
        <!-- Search and Filter -->
        <div class="card mb-4">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-8">
                        <div class="form-outline">
                            <input type="text" id="search" name="search" class="form-control"
                                   value="<?php echo htmlspecialchars($search); ?>">
                            <label class="form-label" for="search"><?php echo __('search_categories', 'Tìm kiếm danh mục...'); ?></label>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <button type="submit" class="btn btn-outline-primary me-2">
                            <i class="fas fa-search me-1"></i><?php echo __('search', 'Tìm kiếm'); ?>
                        </button>
                        <a href="categories.php" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-1"></i><?php echo __('clear', 'Xóa'); ?>
                        </a>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Categories Table -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><?php echo __('category_list', 'Danh sách danh mục'); ?></h5>
                <span class="badge bg-primary">
                    <?php echo __('total', 'Tổng cộng'); ?>: <?php echo $categoriesData['total'] ?? 0; ?>
                </span>
            </div>
            <div class="card-body">
                <?php if (empty($categoriesData['categories'])): ?>
                    <div class="text-center py-4">
                        <i class="fas fa-tags fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted"><?php echo __('no_categories_found', 'Không tìm thấy danh mục'); ?></h5>
                        <p class="text-muted">
                            <?php echo $search ? __('adjust_search_criteria', 'Thử điều chỉnh tiêu chí tìm kiếm.') : __('add_first_category', 'Bắt đầu bằng cách thêm danh mục đầu tiên.'); ?>
                        </p>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th><?php echo __('category_name', 'Tên danh mục'); ?></th>
                                    <th><?php echo __('description', 'Mô tả'); ?></th>
                                    <th><?php echo __('status', 'Trạng thái'); ?></th>
                                    <th><?php echo __('created_at', 'Ngày tạo'); ?></th>
                                    <th><?php echo __('actions', 'Thao tác'); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($categoriesData['categories'] as $category): ?>
                                    <tr>
                                        <td><?php echo $category['category_id']; ?></td>
                                        <td>
                                            <strong><?php echo htmlspecialchars($category['category_name']); ?></strong>
                                        </td>
                                        <td>
                                            <?php echo $category['description'] ?
                                                truncateText(htmlspecialchars($category['description']), 50) :
                                                '<em class="text-muted">' . __('no_description', 'Không có mô tả') . '</em>'; ?>
                                        </td>
                                        <td>
                                            <span class="badge bg-<?php echo $category['status'] ? 'success' : 'danger'; ?>">
                                                <?php echo $category['status'] ? __('active', 'Hoạt động') : __('inactive', 'Không hoạt động'); ?>
                                            </span>
                                        </td>
                                        <td><?php echo formatDate($category['created_at']); ?></td>
                                        <td>
                                            <a href="?edit=<?php echo $category['category_id']; ?>" 
                                               class="btn btn-sm btn-outline-primary btn-action me-1"
                                               data-bs-toggle="tooltip" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" 
                                                    class="btn btn-sm btn-outline-danger btn-action"
                                                    data-bs-toggle="tooltip" title="Delete"
                                                    onclick="confirmDelete(<?php echo $category['category_id']; ?>, '<?php echo htmlspecialchars($category['category_name']); ?>')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    <?php if ($categoriesData['pages'] > 1): ?>
                        <div class="mt-3">
                            <?php 
                            $params = $search ? ['search' => $search] : [];
                            echo generatePagination($categoriesData['current_page'], $categoriesData['pages'], 'categories.php', $params); 
                            ?>
                        </div>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- Category Modal -->
    <div class="modal fade" id="categoryModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-tags me-2"></i>
                        <?php echo $editCategory ? __('edit_category', 'Sửa danh mục') : __('add_new_category', 'Thêm danh mục mới'); ?>
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                        <input type="hidden" name="action" value="<?php echo $editCategory ? 'update' : 'create'; ?>">
                        <?php if ($editCategory): ?>
                            <input type="hidden" name="category_id" value="<?php echo $editCategory['category_id']; ?>">
                        <?php endif; ?>
                        
                        <div class="form-outline mb-3">
                            <input type="text" id="category_name" name="category_name" class="form-control"
                                   value="<?php echo $editCategory ? htmlspecialchars($editCategory['category_name']) : ''; ?>" required>
                            <label class="form-label" for="category_name"><?php echo __('category_name', 'Tên danh mục'); ?> *</label>
                        </div>

                        <div class="form-outline mb-3">
                            <textarea id="description" name="description" class="form-control" rows="3"><?php echo $editCategory ? htmlspecialchars($editCategory['description']) : ''; ?></textarea>
                            <label class="form-label" for="description"><?php echo __('description', 'Mô tả'); ?></label>
                        </div>

                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="status" name="status" value="1"
                                   <?php echo (!$editCategory || $editCategory['status']) ? 'checked' : ''; ?>>
                            <label class="form-check-label" for="status"><?php echo __('active', 'Hoạt động'); ?></label>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?php echo __('cancel', 'Hủy'); ?></button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            <?php echo $editCategory ? __('update', 'Cập nhật') : __('create', 'Tạo mới'); ?>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-exclamation-triangle me-2 text-danger"></i>
                        <?php echo __('confirm_delete', 'Xác nhận xóa'); ?>
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p><?php echo __('confirm_delete_category', 'Bạn có chắc chắn muốn xóa danh mục'); ?> "<span id="deleteCategoryName"></span>"?</p>
                    <p class="text-danger small">
                        <i class="fas fa-warning me-1"></i>
                        <?php echo __('action_cannot_undone_category', 'Hành động này không thể hoàn tác. Danh mục chỉ có thể xóa nếu không có món ăn liên quan.'); ?>
                    </p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?php echo __('cancel', 'Hủy'); ?></button>
                    <form method="POST" style="display: inline;">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                        <input type="hidden" name="action" value="delete">
                        <input type="hidden" name="category_id" id="deleteCategoryId">
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-trash me-2"></i><?php echo __('delete', 'Xóa'); ?>
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <!-- MDBootstrap JS -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/mdb-ui-kit/6.4.2/mdb.min.js"></script>
    
    <script>
        // Show modal if editing
        <?php if ($editCategory): ?>
        document.addEventListener('DOMContentLoaded', function() {
            const modal = new mdb.Modal(document.getElementById('categoryModal'));
            modal.show();
        });
        <?php endif; ?>
        
        // Delete confirmation
        function confirmDelete(categoryId, categoryName) {
            document.getElementById('deleteCategoryId').value = categoryId;
            document.getElementById('deleteCategoryName').textContent = categoryName;
            const modal = new mdb.Modal(document.getElementById('deleteModal'));
            modal.show();
        }
        
        // Initialize tooltips
        document.addEventListener('DOMContentLoaded', function() {
            const tooltips = document.querySelectorAll('[data-bs-toggle="tooltip"]');
            tooltips.forEach(tooltip => {
                new mdb.Tooltip(tooltip);
            });
        });
    </script>
</body>
</html>
