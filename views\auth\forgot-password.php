<?php
/**
 * Password Recovery Page
 * 
 * Allows users to request password reset via email or security questions.
 * Implements secure password recovery with token-based verification.
 */

require_once '../../config/config.php';
require_once '../../config/database.php';
require_once '../../includes/functions.php';
require_once '../../models/User.php';

// Initialize user model
$userModel = new User();

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
        setFlashMessage('error', __('invalid_csrf_token', 'Token bảo mật không hợp lệ. Vui lòng thử lại.'));
    } else {
        $action = $_POST['action'] ?? '';
        
        if ($action === 'request_reset') {
            $identifier = sanitizeInput($_POST['identifier'] ?? ''); // username or email
            
            if (empty($identifier)) {
                setFlashMessage('error', __('identifier_required', 'Vui lòng nhập tên đăng nhập hoặc email.'));
            } else {
                // Find user by username or email
                $user = $userModel->getUserByIdentifier($identifier);
                
                if ($user) {
                    // Generate reset token
                    $resetToken = bin2hex(random_bytes(32));
                    $expiresAt = date('Y-m-d H:i:s', strtotime('+1 hour'));
                    
                    if ($userModel->createPasswordResetToken($user['user_id'], $resetToken, $expiresAt)) {
                        // In a real application, you would send an email here
                        // For demo purposes, we'll show the reset link
                        $resetLink = "http://" . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . "/reset-password.php?token=" . $resetToken;
                        
                        setFlashMessage('success', 
                            __('reset_link_sent', 'Liên kết đặt lại mật khẩu đã được tạo.') . 
                            '<br><strong>' . __('demo_reset_link', 'Liên kết demo') . ':</strong><br>' .
                            '<a href="' . $resetLink . '" class="text-white">' . $resetLink . '</a>'
                        );
                        
                        logActivity("Yêu cầu đặt lại mật khẩu cho: $identifier", $user['user_id']);
                    } else {
                        setFlashMessage('error', __('reset_request_failed', 'Không thể tạo yêu cầu đặt lại mật khẩu. Vui lòng thử lại.'));
                    }
                } else {
                    // Don't reveal if user exists or not for security
                    setFlashMessage('success', __('reset_instructions_sent', 'Nếu tài khoản tồn tại, hướng dẫn đặt lại mật khẩu đã được gửi.'));
                }
            }
        }
    }
}

$pageTitle = __('forgot_password', 'Quên mật khẩu') . ' - ' . APP_NAME;
?>
<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?></title>
    
    <!-- MDBootstrap CSS -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/mdb-ui-kit/6.4.2/mdb.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Roboto', sans-serif;
        }
        .forgot-password-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            max-width: 500px;
            width: 100%;
            margin: 20px;
        }
        .forgot-password-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px 30px;
            text-align: center;
        }
        .forgot-password-body {
            padding: 40px 30px;
        }
        .brand-logo {
            font-size: 3rem;
            margin-bottom: 20px;
        }
        .form-outline {
            margin-bottom: 20px;
        }
        .btn-reset {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        .btn-reset:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        .back-to-login {
            text-align: center;
            margin-top: 20px;
        }
        .back-to-login a {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
        }
        .back-to-login a:hover {
            text-decoration: underline;
        }
        .security-info {
            background: #e7f3ff;
            border: 1px solid #b3d7ff;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .security-info h6 {
            color: #004085;
            margin-bottom: 10px;
        }
        .security-info ul {
            color: #004085;
            margin-bottom: 0;
            padding-left: 20px;
        }
    </style>
</head>
<body>
    <div class="forgot-password-container">
        <!-- Header -->
        <div class="forgot-password-header">
            <div class="brand-logo">
                <i class="fas fa-utensils"></i>
            </div>
            <h3><?php echo APP_NAME; ?></h3>
            <p class="mb-0"><?php echo __('password_recovery', 'Khôi phục mật khẩu'); ?></p>
        </div>
        
        <!-- Body -->
        <div class="forgot-password-body">
            <?php displayFlashMessage(); ?>
            
            <div class="text-center mb-4">
                <h4><?php echo __('forgot_password', 'Quên mật khẩu'); ?></h4>
                <p class="text-muted">
                    <?php echo __('forgot_password_instruction', 'Nhập tên đăng nhập hoặc email để nhận hướng dẫn đặt lại mật khẩu.'); ?>
                </p>
            </div>
            
            <!-- Security Information -->
            <div class="security-info">
                <h6><i class="fas fa-shield-alt me-2"></i><?php echo __('security_notice', 'Thông báo bảo mật'); ?></h6>
                <ul>
                    <li><?php echo __('reset_link_expires', 'Liên kết đặt lại có hiệu lực trong 1 giờ'); ?></li>
                    <li><?php echo __('one_time_use', 'Mỗi liên kết chỉ sử dụng được một lần'); ?></li>
                    <li><?php echo __('secure_process', 'Quá trình được mã hóa và bảo mật'); ?></li>
                </ul>
            </div>
            
            <!-- Reset Form -->
            <form method="POST" id="forgotPasswordForm">
                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                <input type="hidden" name="action" value="request_reset">
                
                <div class="form-outline">
                    <input type="text" id="identifier" name="identifier" class="form-control" required>
                    <label class="form-label" for="identifier">
                        <i class="fas fa-user me-2"></i><?php echo __('username_or_email', 'Tên đăng nhập hoặc Email'); ?>
                    </label>
                </div>
                
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary btn-reset">
                        <i class="fas fa-paper-plane me-2"></i>
                        <?php echo __('send_reset_instructions', 'Gửi hướng dẫn đặt lại'); ?>
                    </button>
                </div>
            </form>
            
            <!-- Alternative Options -->
            <div class="mt-4">
                <div class="text-center">
                    <h6 class="text-muted"><?php echo __('alternative_options', 'Tùy chọn khác'); ?></h6>
                </div>
                
                <div class="row mt-3">
                    <div class="col-6">
                        <div class="text-center">
                            <i class="fas fa-phone fa-2x text-primary mb-2"></i>
                            <h6><?php echo __('contact_admin', 'Liên hệ quản trị'); ?></h6>
                            <small class="text-muted"><?php echo __('call_for_help', 'Gọi để được hỗ trợ'); ?></small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center">
                            <i class="fas fa-question-circle fa-2x text-info mb-2"></i>
                            <h6><?php echo __('security_questions', 'Câu hỏi bảo mật'); ?></h6>
                            <small class="text-muted"><?php echo __('coming_soon', 'Sắp ra mắt'); ?></small>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Back to Login -->
            <div class="back-to-login">
                <p class="mb-0">
                    <?php echo __('remember_password', 'Nhớ lại mật khẩu?'); ?>
                    <a href="login.php">
                        <i class="fas fa-sign-in-alt me-1"></i><?php echo __('back_to_login', 'Quay về đăng nhập'); ?>
                    </a>
                </p>
            </div>
        </div>
    </div>
    
    <!-- MDBootstrap JS -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/mdb-ui-kit/6.4.2/mdb.min.js"></script>
    
    <script>
        // Form validation and enhancement
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('forgotPasswordForm');
            const identifierInput = document.getElementById('identifier');
            
            // Auto-detect if input is email or username
            identifierInput.addEventListener('input', function() {
                const value = this.value;
                const label = this.nextElementSibling;
                
                if (value.includes('@')) {
                    label.innerHTML = '<i class="fas fa-envelope me-2"></i><?php echo __('email', 'Email'); ?>';
                } else {
                    label.innerHTML = '<i class="fas fa-user me-2"></i><?php echo __('username', 'Tên đăng nhập'); ?>';
                }
            });
            
            // Form submission handling
            form.addEventListener('submit', function(e) {
                const identifier = identifierInput.value.trim();
                
                if (!identifier) {
                    e.preventDefault();
                    alert('<?php echo __('please_enter_identifier', 'Vui lòng nhập tên đăng nhập hoặc email.'); ?>');
                    return;
                }
                
                // Show loading state
                const submitBtn = form.querySelector('button[type="submit"]');
                const originalText = submitBtn.innerHTML;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i><?php echo __('sending', 'Đang gửi...'); ?>';
                submitBtn.disabled = true;
                
                // Re-enable button after 3 seconds (in case of error)
                setTimeout(() => {
                    submitBtn.innerHTML = originalText;
                    submitBtn.disabled = false;
                }, 3000);
            });
        });
    </script>
</body>
</html>
