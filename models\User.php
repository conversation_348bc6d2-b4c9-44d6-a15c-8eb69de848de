<?php
/**
 * User Model
 * 
 * Handles all user-related database operations including authentication,
 * user management, and role-based access control.
 */

require_once __DIR__ . '/../config/database.php';

class User {
    private $pdo;
    private $table = 'users';

    public function __construct() {
        global $pdo;
        $this->pdo = $pdo;
    }

    /**
     * Authenticate user login
     * 
     * @param string $username Username or email
     * @param string $password Plain text password
     * @return array|false User data on success, false on failure
     */
    public function authenticate($username, $password) {
        try {
            $sql = "SELECT u.*, r.role_name
                    FROM {$this->table} u
                    JOIN roles r ON u.role_id = r.role_id
                    WHERE (u.username = ? OR u.email = ?)
                    AND u.status = 1";

            $stmt = $this->pdo->prepare($sql);
            $stmt->execute([$username, $username]);

            $user = $stmt->fetch();

            if ($user && password_verify($password, $user['password'])) {
                // Remove password from returned data
                unset($user['password']);
                return $user;
            }

            return false;
        } catch (PDOException $e) {
            error_log("Authentication error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get user by ID
     * 
     * @param int $userId User ID
     * @return array|false User data or false
     */
    public function getUserById($userId) {
        try {
            $sql = "SELECT u.*, r.role_name 
                    FROM {$this->table} u 
                    JOIN roles r ON u.role_id = r.role_id 
                    WHERE u.user_id = :user_id";
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->bindParam(':user_id', $userId);
            $stmt->execute();
            
            $user = $stmt->fetch();
            if ($user) {
                unset($user['password']);
            }
            
            return $user;
        } catch (PDOException $e) {
            error_log("Get user error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get all users with pagination
     * 
     * @param int $page Page number
     * @param int $limit Items per page
     * @return array Users data with pagination info
     */
    public function getAllUsers($page = 1, $limit = ITEMS_PER_PAGE) {
        try {
            $offset = ($page - 1) * $limit;
            
            // Get total count
            $countSql = "SELECT COUNT(*) as total FROM {$this->table}";
            $countStmt = $this->pdo->prepare($countSql);
            $countStmt->execute();
            $totalUsers = $countStmt->fetch()['total'];
            
            // Get users
            $sql = "SELECT u.user_id, u.username, u.email, u.full_name, u.status, 
                           u.created_at, r.role_name 
                    FROM {$this->table} u 
                    JOIN roles r ON u.role_id = r.role_id 
                    ORDER BY u.created_at DESC 
                    LIMIT :limit OFFSET :offset";
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
            $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
            $stmt->execute();
            
            $users = $stmt->fetchAll();
            
            return [
                'users' => $users,
                'total' => $totalUsers,
                'pages' => ceil($totalUsers / $limit),
                'current_page' => $page
            ];
        } catch (PDOException $e) {
            error_log("Get all users error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Create new user
     * 
     * @param array $userData User data
     * @return int|false User ID on success, false on failure
     */
    public function createUser($userData) {
        try {
            // Check if username or email already exists
            if ($this->usernameExists($userData['username']) || $this->emailExists($userData['email'])) {
                return false;
            }

            $sql = "INSERT INTO {$this->table} (username, password, email, full_name, role_id, status) 
                    VALUES (:username, :password, :email, :full_name, :role_id, :status)";
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->bindParam(':username', $userData['username']);
            $stmt->bindParam(':password', password_hash($userData['password'], PASSWORD_DEFAULT));
            $stmt->bindParam(':email', $userData['email']);
            $stmt->bindParam(':full_name', $userData['full_name']);
            $stmt->bindParam(':role_id', $userData['role_id']);
            $stmt->bindParam(':status', $userData['status']);
            
            if ($stmt->execute()) {
                return $this->pdo->lastInsertId();
            }
            
            return false;
        } catch (PDOException $e) {
            error_log("Create user error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Update user
     * 
     * @param int $userId User ID
     * @param array $userData User data
     * @return bool Success status
     */
    public function updateUser($userId, $userData) {
        try {
            $sql = "UPDATE {$this->table} 
                    SET username = :username, email = :email, full_name = :full_name, 
                        role_id = :role_id, status = :status 
                    WHERE user_id = :user_id";
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->bindParam(':username', $userData['username']);
            $stmt->bindParam(':email', $userData['email']);
            $stmt->bindParam(':full_name', $userData['full_name']);
            $stmt->bindParam(':role_id', $userData['role_id']);
            $stmt->bindParam(':status', $userData['status']);
            $stmt->bindParam(':user_id', $userId);
            
            return $stmt->execute();
        } catch (PDOException $e) {
            error_log("Update user error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Update user password
     * 
     * @param int $userId User ID
     * @param string $newPassword New password
     * @return bool Success status
     */
    public function updatePassword($userId, $newPassword) {
        try {
            $sql = "UPDATE {$this->table} SET password = :password WHERE user_id = :user_id";
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->bindParam(':password', password_hash($newPassword, PASSWORD_DEFAULT));
            $stmt->bindParam(':user_id', $userId);
            
            return $stmt->execute();
        } catch (PDOException $e) {
            error_log("Update password error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Delete user
     * 
     * @param int $userId User ID
     * @return bool Success status
     */
    public function deleteUser($userId) {
        try {
            $sql = "DELETE FROM {$this->table} WHERE user_id = :user_id";
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->bindParam(':user_id', $userId);
            
            return $stmt->execute();
        } catch (PDOException $e) {
            error_log("Delete user error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Check if username exists
     * 
     * @param string $username Username
     * @param int $excludeUserId User ID to exclude from check
     * @return bool
     */
    public function usernameExists($username, $excludeUserId = null) {
        try {
            $sql = "SELECT COUNT(*) as count FROM {$this->table} WHERE username = :username";
            if ($excludeUserId) {
                $sql .= " AND user_id != :exclude_id";
            }
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->bindParam(':username', $username);
            if ($excludeUserId) {
                $stmt->bindParam(':exclude_id', $excludeUserId);
            }
            $stmt->execute();
            
            return $stmt->fetch()['count'] > 0;
        } catch (PDOException $e) {
            error_log("Username exists check error: " . $e->getMessage());
            return true; // Assume exists on error for safety
        }
    }

    /**
     * Check if email exists
     * 
     * @param string $email Email
     * @param int $excludeUserId User ID to exclude from check
     * @return bool
     */
    public function emailExists($email, $excludeUserId = null) {
        try {
            $sql = "SELECT COUNT(*) as count FROM {$this->table} WHERE email = :email";
            if ($excludeUserId) {
                $sql .= " AND user_id != :exclude_id";
            }
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->bindParam(':email', $email);
            if ($excludeUserId) {
                $stmt->bindParam(':exclude_id', $excludeUserId);
            }
            $stmt->execute();
            
            return $stmt->fetch()['count'] > 0;
        } catch (PDOException $e) {
            error_log("Email exists check error: " . $e->getMessage());
            return true; // Assume exists on error for safety
        }
    }

    /**
     * Get all roles
     * 
     * @return array Roles data
     */
    public function getAllRoles() {
        try {
            $sql = "SELECT * FROM roles ORDER BY role_name";
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute();
            
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("Get roles error: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Set password reset token
     * 
     * @param string $email User email
     * @param string $token Reset token
     * @return bool Success status
     */
    public function setResetToken($email, $token) {
        try {
            $expires = date('Y-m-d H:i:s', strtotime('+1 hour'));
            
            $sql = "UPDATE {$this->table} 
                    SET reset_token = :token, reset_token_expires = :expires 
                    WHERE email = :email";
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->bindParam(':token', $token);
            $stmt->bindParam(':expires', $expires);
            $stmt->bindParam(':email', $email);
            
            return $stmt->execute();
        } catch (PDOException $e) {
            error_log("Set reset token error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Verify reset token
     * 
     * @param string $token Reset token
     * @return array|false User data or false
     */
    public function verifyResetToken($token) {
        try {
            $sql = "SELECT * FROM {$this->table} 
                    WHERE reset_token = :token 
                    AND reset_token_expires > NOW() 
                    AND status = 1";
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->bindParam(':token', $token);
            $stmt->execute();
            
            return $stmt->fetch();
        } catch (PDOException $e) {
            error_log("Verify reset token error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Clear reset token
     * 
     * @param int $userId User ID
     * @return bool Success status
     */
    public function clearResetToken($userId) {
        try {
            $sql = "UPDATE {$this->table} 
                    SET reset_token = NULL, reset_token_expires = NULL 
                    WHERE user_id = :user_id";
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->bindParam(':user_id', $userId);
            
            return $stmt->execute();
        } catch (PDOException $e) {
            error_log("Clear reset token error: " . $e->getMessage());
            return false;
        }
    }
}
?>
