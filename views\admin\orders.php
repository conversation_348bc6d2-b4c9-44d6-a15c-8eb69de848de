<?php
/**
 * Admin Orders Management Page
 * 
 * Admin interface for viewing and managing all orders with advanced
 * filtering, statistics, and order management capabilities.
 */

require_once '../../config/config.php';
require_once '../../config/database.php';
require_once '../../includes/functions.php';
require_once '../../models/Order.php';
require_once '../../models/Payment.php';

// Check admin access
requireAdmin();

// Initialize models
$orderModel = new Order();
$paymentModel = new Payment();

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
        setFlashMessage('error', __('invalid_csrf_token', 'Token bảo mật không hợp lệ. Vui lòng thử lại.'));
    } else {
        $action = $_POST['action'] ?? '';
        
        switch ($action) {
            case 'update_status':
                $orderId = (int)($_POST['order_id'] ?? 0);
                $newStatus = sanitizeInput($_POST['status'] ?? '');
                $validStatuses = ['pending', 'processing', 'completed', 'cancelled'];
                
                if ($orderId > 0 && in_array($newStatus, $validStatuses)) {
                    if ($orderModel->updateOrderStatus($orderId, $newStatus)) {
                        setFlashMessage('success', __('order_status_updated', 'Cập nhật trạng thái đơn hàng thành công.'));
                        logActivity("Admin cập nhật trạng thái đơn hàng #$orderId thành $newStatus", $_SESSION['user_id']);
                    } else {
                        setFlashMessage('error', __('order_status_update_failed', 'Cập nhật trạng thái thất bại.'));
                    }
                } else {
                    setFlashMessage('error', __('invalid_data', 'Dữ liệu không hợp lệ.'));
                }
                break;
                
            case 'delete_order':
                $orderId = (int)($_POST['order_id'] ?? 0);
                if ($orderId > 0) {
                    if ($orderModel->deleteOrder($orderId)) {
                        setFlashMessage('success', __('order_deleted_success', 'Xóa đơn hàng thành công.'));
                        logActivity("Admin xóa đơn hàng #$orderId", $_SESSION['user_id']);
                    } else {
                        setFlashMessage('error', __('order_delete_failed', 'Xóa đơn hàng thất bại.'));
                    }
                }
                break;
        }
        
        // Redirect to prevent form resubmission
        header('Location: orders.php');
        exit();
    }
}

// Get filter parameters
$page = (int)($_GET['page'] ?? 1);
$search = sanitizeInput($_GET['search'] ?? '');
$statusFilter = sanitizeInput($_GET['status'] ?? '');
$dateFrom = sanitizeInput($_GET['date_from'] ?? '');
$dateTo = sanitizeInput($_GET['date_to'] ?? '');
$staffFilter = (int)($_GET['staff'] ?? 0);

// Build filter conditions
$filters = [];
if ($search) $filters['search'] = $search;
if ($statusFilter) $filters['status'] = $statusFilter;
if ($dateFrom) $filters['date_from'] = $dateFrom;
if ($dateTo) $filters['date_to'] = $dateTo;
if ($staffFilter) $filters['staff_id'] = $staffFilter;

// Get orders with filters
if (!empty($filters)) {
    $ordersData = $orderModel->searchOrders($filters, $page);
} else {
    $ordersData = $orderModel->getAllOrders($page, ITEMS_PER_PAGE);
}

// Get order statistics
$orderStats = $orderModel->getOrderStatistics();

// Get staff list for filter
$staffList = $orderModel->getStaffList();

$pageTitle = __('orders_management', 'Quản lý đơn hàng') . ' - ' . APP_NAME;
?>
<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?></title>
    
    <!-- MDBootstrap CSS -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/mdb-ui-kit/6.4.2/mdb.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="../../assets/css/custom.css" rel="stylesheet">
    
    <style>
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            transition: transform 0.3s ease;
        }
        .stats-card:hover {
            transform: translateY(-5px);
        }
        .order-status-badge {
            font-size: 0.8rem;
            padding: 4px 12px;
            border-radius: 15px;
        }
        .filter-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .order-actions {
            display: flex;
            gap: 5px;
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <nav class="sidebar">
        <div class="p-3">
            <div class="text-center mb-4">
                <i class="fas fa-utensils fa-2x text-white mb-2"></i>
                <h5 class="text-white mb-0"><?php echo APP_NAME; ?></h5>
                <small class="text-white-50"><?php echo __('admin', 'Quản trị viên'); ?></small>
            </div>
            
            <div class="mb-3">
                <small class="text-white-50 text-uppercase"><?php echo __('dashboard_welcome', 'Chào mừng'); ?></small>
                <div class="text-white">
                    <i class="fas fa-user-shield me-2"></i>
                    <?php echo htmlspecialchars($_SESSION['full_name']); ?>
                </div>
            </div>
            
            <hr class="text-white-50">
            
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link" href="dashboard.php">
                        <i class="fas fa-tachometer-alt me-2"></i><?php echo __('dashboard', 'Bảng điều khiển'); ?>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="categories.php">
                        <i class="fas fa-tags me-2"></i><?php echo __('categories', 'Danh mục'); ?>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="food-items.php">
                        <i class="fas fa-hamburger me-2"></i><?php echo __('food_items', 'Món ăn'); ?>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="tables.php">
                        <i class="fas fa-chair me-2"></i><?php echo __('tables', 'Bàn'); ?>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="users.php">
                        <i class="fas fa-users me-2"></i><?php echo __('users', 'Người dùng'); ?>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link active" href="orders.php">
                        <i class="fas fa-receipt me-2"></i><?php echo __('orders', 'Đơn hàng'); ?>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="reports.php">
                        <i class="fas fa-chart-bar me-2"></i><?php echo __('reports', 'Báo cáo'); ?>
                    </a>
                </li>
            </ul>
            
            <hr class="text-white-50">
            
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link" href="../auth/logout.php">
                        <i class="fas fa-sign-out-alt me-2"></i><?php echo __('logout', 'Đăng xuất'); ?>
                    </a>
                </li>
            </ul>
        </div>
    </nav>
    
    <!-- Main Content -->
    <div class="main-content">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2><i class="fas fa-receipt me-2"></i><?php echo __('orders_management', 'Quản lý đơn hàng'); ?></h2>
            <div>
                <button type="button" class="btn btn-outline-primary me-2" onclick="window.location.reload()">
                    <i class="fas fa-sync-alt me-1"></i><?php echo __('refresh', 'Làm mới'); ?>
                </button>
                <a href="reports.php" class="btn btn-info">
                    <i class="fas fa-chart-line me-2"></i><?php echo __('view_reports', 'Xem báo cáo'); ?>
                </a>
            </div>
        </div>
        
        <?php displayFlashMessage(); ?>
        
        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <i class="fas fa-receipt fa-2x mb-2"></i>
                        <h3><?php echo $orderStats['total_orders'] ?? 0; ?></h3>
                        <p class="mb-0"><?php echo __('total_orders', 'Tổng đơn hàng'); ?></p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <i class="fas fa-clock fa-2x mb-2"></i>
                        <h3><?php echo $orderStats['pending_orders'] ?? 0; ?></h3>
                        <p class="mb-0"><?php echo __('pending_orders', 'Đơn chờ xử lý'); ?></p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <i class="fas fa-check fa-2x mb-2"></i>
                        <h3><?php echo $orderStats['completed_orders'] ?? 0; ?></h3>
                        <p class="mb-0"><?php echo __('completed_orders', 'Đã hoàn thành'); ?></p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <i class="fas fa-money-bill-wave fa-2x mb-2"></i>
                        <h3><?php echo formatCurrency($orderStats['total_revenue'] ?? 0); ?></h3>
                        <p class="mb-0"><?php echo __('total_revenue', 'Tổng doanh thu'); ?></p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Advanced Filters -->
        <div class="filter-section">
            <h5 class="mb-3"><i class="fas fa-filter me-2"></i><?php echo __('advanced_filters', 'Bộ lọc nâng cao'); ?></h5>
            <form method="GET" class="row g-3">
                <div class="col-md-3">
                    <div class="form-outline">
                        <input type="text" id="search" name="search" class="form-control" 
                               value="<?php echo htmlspecialchars($search); ?>">
                        <label class="form-label" for="search"><?php echo __('search_orders', 'Tìm kiếm đơn hàng...'); ?></label>
                    </div>
                </div>
                <div class="col-md-2">
                    <select id="status" name="status" class="form-select">
                        <option value=""><?php echo __('all_statuses', 'Tất cả trạng thái'); ?></option>
                        <option value="pending" <?php echo $statusFilter === 'pending' ? 'selected' : ''; ?>><?php echo __('order_status_pending', 'Chờ xử lý'); ?></option>
                        <option value="processing" <?php echo $statusFilter === 'processing' ? 'selected' : ''; ?>><?php echo __('order_status_processing', 'Đang xử lý'); ?></option>
                        <option value="completed" <?php echo $statusFilter === 'completed' ? 'selected' : ''; ?>><?php echo __('order_status_completed', 'Hoàn thành'); ?></option>
                        <option value="cancelled" <?php echo $statusFilter === 'cancelled' ? 'selected' : ''; ?>><?php echo __('order_status_cancelled', 'Đã hủy'); ?></option>
                    </select>
                </div>
                <div class="col-md-2">
                    <select id="staff" name="staff" class="form-select">
                        <option value=""><?php echo __('all_staff', 'Tất cả nhân viên'); ?></option>
                        <?php foreach ($staffList as $staff): ?>
                            <option value="<?php echo $staff['user_id']; ?>" 
                                    <?php echo $staffFilter == $staff['user_id'] ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($staff['full_name']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="col-md-2">
                    <input type="date" id="date_from" name="date_from" class="form-control" 
                           value="<?php echo htmlspecialchars($dateFrom); ?>" 
                           placeholder="<?php echo __('from_date', 'Từ ngày'); ?>">
                </div>
                <div class="col-md-2">
                    <input type="date" id="date_to" name="date_to" class="form-control" 
                           value="<?php echo htmlspecialchars($dateTo); ?>" 
                           placeholder="<?php echo __('to_date', 'Đến ngày'); ?>">
                </div>
                <div class="col-md-1">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </form>
            <div class="mt-2">
                <a href="orders.php" class="btn btn-outline-secondary btn-sm">
                    <i class="fas fa-times me-1"></i><?php echo __('clear_filters', 'Xóa bộ lọc'); ?>
                </a>
            </div>
        </div>
        
        <!-- Orders Table -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><?php echo __('orders_list', 'Danh sách đơn hàng'); ?></h5>
                <span class="badge bg-primary">
                    <?php echo __('total', 'Tổng cộng'); ?>: <?php echo $ordersData['total'] ?? 0; ?>
                </span>
            </div>
            <div class="card-body">
                <?php if (empty($ordersData['orders'])): ?>
                    <div class="text-center py-4">
                        <i class="fas fa-receipt fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted"><?php echo __('no_orders_found', 'Không tìm thấy đơn hàng'); ?></h5>
                        <p class="text-muted">
                            <?php echo __('adjust_search_criteria', 'Thử điều chỉnh tiêu chí tìm kiếm.'); ?>
                        </p>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th><?php echo __('table', 'Bàn'); ?></th>
                                    <th><?php echo __('customer', 'Khách hàng'); ?></th>
                                    <th><?php echo __('staff', 'Nhân viên'); ?></th>
                                    <th><?php echo __('total_amount', 'Tổng tiền'); ?></th>
                                    <th><?php echo __('status', 'Trạng thái'); ?></th>
                                    <th><?php echo __('created_at', 'Ngày tạo'); ?></th>
                                    <th><?php echo __('actions', 'Thao tác'); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($ordersData['orders'] as $order): ?>
                                    <tr>
                                        <td><strong>#<?php echo $order['order_id']; ?></strong></td>
                                        <td>
                                            <i class="fas fa-chair me-1"></i>
                                            <?php echo __('table', 'Bàn'); ?> <?php echo htmlspecialchars($order['table_number']); ?>
                                        </td>
                                        <td>
                                            <?php if ($order['customer_name']): ?>
                                                <strong><?php echo htmlspecialchars($order['customer_name']); ?></strong>
                                                <?php if ($order['customer_phone']): ?>
                                                    <br><small class="text-muted"><?php echo htmlspecialchars($order['customer_phone']); ?></small>
                                                <?php endif; ?>
                                            <?php else: ?>
                                                <em class="text-muted"><?php echo __('walk_in_customer', 'Khách vãng lai'); ?></em>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo htmlspecialchars($order['staff_name']); ?></td>
                                        <td><strong class="text-primary"><?php echo formatCurrency($order['total_amount']); ?></strong></td>
                                        <td>
                                            <span class="badge order-status-badge bg-<?php 
                                                echo $order['status'] === 'pending' ? 'warning' : 
                                                    ($order['status'] === 'processing' ? 'info' : 
                                                    ($order['status'] === 'completed' ? 'success' : 'danger')); 
                                            ?>">
                                                <?php echo __('order_status_' . $order['status'], ucfirst($order['status'])); ?>
                                            </span>
                                        </td>
                                        <td><?php echo formatVietnameseDate($order['created_at']); ?></td>
                                        <td>
                                            <div class="order-actions">
                                                <a href="../staff/order-details.php?id=<?php echo $order['order_id']; ?>" 
                                                   class="btn btn-sm btn-outline-primary"
                                                   data-bs-toggle="tooltip" title="<?php echo __('view_details', 'Xem chi tiết'); ?>">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                
                                                <?php if ($order['status'] !== 'cancelled'): ?>
                                                    <div class="dropdown">
                                                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                                                                type="button" data-bs-toggle="dropdown">
                                                            <i class="fas fa-cog"></i>
                                                        </button>
                                                        <ul class="dropdown-menu">
                                                            <li><h6 class="dropdown-header"><?php echo __('change_status', 'Thay đổi trạng thái'); ?></h6></li>
                                                            <?php if ($order['status'] !== 'pending'): ?>
                                                                <li><a class="dropdown-item" href="#" onclick="updateOrderStatus(<?php echo $order['order_id']; ?>, 'pending')">
                                                                    <i class="fas fa-clock me-2"></i><?php echo __('order_status_pending', 'Chờ xử lý'); ?>
                                                                </a></li>
                                                            <?php endif; ?>
                                                            <?php if ($order['status'] !== 'processing'): ?>
                                                                <li><a class="dropdown-item" href="#" onclick="updateOrderStatus(<?php echo $order['order_id']; ?>, 'processing')">
                                                                    <i class="fas fa-cog me-2"></i><?php echo __('order_status_processing', 'Đang xử lý'); ?>
                                                                </a></li>
                                                            <?php endif; ?>
                                                            <?php if ($order['status'] !== 'completed'): ?>
                                                                <li><a class="dropdown-item" href="#" onclick="updateOrderStatus(<?php echo $order['order_id']; ?>, 'completed')">
                                                                    <i class="fas fa-check me-2"></i><?php echo __('order_status_completed', 'Hoàn thành'); ?>
                                                                </a></li>
                                                            <?php endif; ?>
                                                            <li><hr class="dropdown-divider"></li>
                                                            <li><a class="dropdown-item text-danger" href="#" onclick="updateOrderStatus(<?php echo $order['order_id']; ?>, 'cancelled')">
                                                                <i class="fas fa-times me-2"></i><?php echo __('order_status_cancelled', 'Hủy đơn'); ?>
                                                            </a></li>
                                                        </ul>
                                                    </div>
                                                <?php endif; ?>
                                                
                                                <button type="button" class="btn btn-sm btn-outline-danger"
                                                        onclick="confirmDeleteOrder(<?php echo $order['order_id']; ?>)"
                                                        data-bs-toggle="tooltip" title="<?php echo __('delete', 'Xóa'); ?>">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    <?php if ($ordersData['pages'] > 1): ?>
                        <div class="mt-3">
                            <?php 
                            $params = [];
                            if ($search) $params['search'] = $search;
                            if ($statusFilter) $params['status'] = $statusFilter;
                            if ($dateFrom) $params['date_from'] = $dateFrom;
                            if ($dateTo) $params['date_to'] = $dateTo;
                            if ($staffFilter) $params['staff'] = $staffFilter;
                            echo generatePagination($ordersData['current_page'], $ordersData['pages'], 'orders.php', $params); 
                            ?>
                        </div>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- Hidden Forms for Actions -->
    <form id="statusUpdateForm" method="POST" style="display: none;">
        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
        <input type="hidden" name="action" value="update_status">
        <input type="hidden" name="order_id" id="statusOrderId">
        <input type="hidden" name="status" id="statusValue">
    </form>
    
    <form id="deleteOrderForm" method="POST" style="display: none;">
        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
        <input type="hidden" name="action" value="delete_order">
        <input type="hidden" name="order_id" id="deleteOrderId">
    </form>
    
    <!-- MDBootstrap JS -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/mdb-ui-kit/6.4.2/mdb.min.js"></script>
    <!-- Custom JS -->
    <script src="../../assets/js/app.js"></script>
    
    <script>
        function updateOrderStatus(orderId, status) {
            const statusNames = {
                'pending': '<?php echo __('order_status_pending', 'Chờ xử lý'); ?>',
                'processing': '<?php echo __('order_status_processing', 'Đang xử lý'); ?>',
                'completed': '<?php echo __('order_status_completed', 'Hoàn thành'); ?>',
                'cancelled': '<?php echo __('order_status_cancelled', 'Đã hủy'); ?>'
            };
            
            if (confirm(`<?php echo __('confirm_status_change', 'Bạn có chắc chắn muốn thay đổi trạng thái đơn hàng thành'); ?> "${statusNames[status]}"?`)) {
                document.getElementById('statusOrderId').value = orderId;
                document.getElementById('statusValue').value = status;
                document.getElementById('statusUpdateForm').submit();
            }
        }
        
        function confirmDeleteOrder(orderId) {
            if (confirm('<?php echo __('confirm_delete_order', 'Bạn có chắc chắn muốn xóa đơn hàng này? Hành động này không thể hoàn tác.'); ?>')) {
                document.getElementById('deleteOrderId').value = orderId;
                document.getElementById('deleteOrderForm').submit();
            }
        }
        
        // Initialize tooltips
        document.addEventListener('DOMContentLoaded', function() {
            const tooltips = document.querySelectorAll('[data-bs-toggle="tooltip"]');
            tooltips.forEach(tooltip => {
                new mdb.Tooltip(tooltip);
            });
        });
    </script>
</body>
</html>
