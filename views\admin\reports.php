<?php
/**
 * Reports & Analytics Page
 * 
 * Admin interface for viewing revenue reports, sales analytics,
 * popular items analysis, and table utilization statistics.
 */

require_once '../../config/config.php';
require_once '../../config/database.php';
require_once '../../includes/functions.php';
require_once '../../models/Order.php';
require_once '../../models/FoodItem.php';
require_once '../../models/Table.php';
require_once '../../models/Payment.php';

// Check admin access
requireAdmin();

// Initialize models
$orderModel = new Order();
$foodItemModel = new FoodItem();
$tableModel = new Table();
$paymentModel = new Payment();

// Get date range from request
$dateFrom = sanitizeInput($_GET['date_from'] ?? date('Y-m-01')); // First day of current month
$dateTo = sanitizeInput($_GET['date_to'] ?? date('Y-m-d')); // Today
$reportType = sanitizeInput($_GET['report_type'] ?? 'overview');

// Get reports data
$revenueReport = $orderModel->getRevenueReport($dateFrom, $dateTo);
$popularItems = $foodItemModel->getPopularItems($dateFrom, $dateTo, 10);
$tableUtilization = $tableModel->getTableUtilization($dateFrom, $dateTo);
$dailyRevenue = $orderModel->getDailyRevenue($dateFrom, $dateTo);
$paymentMethods = $paymentModel->getPaymentMethodStats($dateFrom, $dateTo);
$staffPerformance = $orderModel->getStaffPerformance($dateFrom, $dateTo);

$pageTitle = __('reports_analytics', 'Báo cáo & Phân tích') . ' - ' . APP_NAME;
?>
<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?></title>
    
    <!-- MDBootstrap CSS -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/mdb-ui-kit/6.4.2/mdb.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Custom CSS -->
    <link href="../../assets/css/custom.css" rel="stylesheet">
    
    <style>
        .report-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            transition: transform 0.3s ease;
        }
        .report-card:hover {
            transform: translateY(-5px);
        }
        .chart-container {
            position: relative;
            height: 400px;
            margin: 20px 0;
        }
        .report-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .metric-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        .metric-card:hover {
            transform: translateY(-3px);
        }
        .metric-value {
            font-size: 2rem;
            font-weight: bold;
            color: #007bff;
        }
        .metric-label {
            color: #6c757d;
            font-size: 0.9rem;
        }
        .progress-custom {
            height: 8px;
            border-radius: 10px;
        }
        .table-utilization {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .table-card {
            background: white;
            border-radius: 10px;
            padding: 15px;
            text-align: center;
            border-left: 4px solid #007bff;
        }
        .export-buttons {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <nav class="sidebar">
        <div class="p-3">
            <div class="text-center mb-4">
                <i class="fas fa-utensils fa-2x text-white mb-2"></i>
                <h5 class="text-white mb-0"><?php echo APP_NAME; ?></h5>
                <small class="text-white-50"><?php echo __('admin', 'Quản trị viên'); ?></small>
            </div>
            
            <div class="mb-3">
                <small class="text-white-50 text-uppercase"><?php echo __('dashboard_welcome', 'Chào mừng'); ?></small>
                <div class="text-white">
                    <i class="fas fa-user-shield me-2"></i>
                    <?php echo htmlspecialchars($_SESSION['full_name']); ?>
                </div>
            </div>
            
            <hr class="text-white-50">
            
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link" href="dashboard.php">
                        <i class="fas fa-tachometer-alt me-2"></i><?php echo __('dashboard', 'Bảng điều khiển'); ?>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="categories.php">
                        <i class="fas fa-tags me-2"></i><?php echo __('categories', 'Danh mục'); ?>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="food-items.php">
                        <i class="fas fa-hamburger me-2"></i><?php echo __('food_items', 'Món ăn'); ?>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="tables.php">
                        <i class="fas fa-chair me-2"></i><?php echo __('tables', 'Bàn'); ?>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="users.php">
                        <i class="fas fa-users me-2"></i><?php echo __('users', 'Người dùng'); ?>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="orders.php">
                        <i class="fas fa-receipt me-2"></i><?php echo __('orders', 'Đơn hàng'); ?>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link active" href="reports.php">
                        <i class="fas fa-chart-bar me-2"></i><?php echo __('reports', 'Báo cáo'); ?>
                    </a>
                </li>
            </ul>
            
            <hr class="text-white-50">
            
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link" href="../auth/logout.php">
                        <i class="fas fa-sign-out-alt me-2"></i><?php echo __('logout', 'Đăng xuất'); ?>
                    </a>
                </li>
            </ul>
        </div>
    </nav>
    
    <!-- Main Content -->
    <div class="main-content">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2><i class="fas fa-chart-bar me-2"></i><?php echo __('reports_analytics', 'Báo cáo & Phân tích'); ?></h2>
            <div class="export-buttons">
                <button type="button" class="btn btn-success" onclick="exportToExcel()">
                    <i class="fas fa-file-excel me-2"></i><?php echo __('export_excel', 'Xuất Excel'); ?>
                </button>
                <button type="button" class="btn btn-danger" onclick="exportToPDF()">
                    <i class="fas fa-file-pdf me-2"></i><?php echo __('export_pdf', 'Xuất PDF'); ?>
                </button>
                <button type="button" class="btn btn-info" onclick="printReport()">
                    <i class="fas fa-print me-2"></i><?php echo __('print', 'In báo cáo'); ?>
                </button>
            </div>
        </div>
        
        <!-- Date Range Filter -->
        <div class="report-section">
            <h5 class="mb-3"><i class="fas fa-calendar me-2"></i><?php echo __('report_period', 'Khoảng thời gian báo cáo'); ?></h5>
            <form method="GET" class="row g-3">
                <div class="col-md-3">
                    <label for="date_from" class="form-label"><?php echo __('from_date', 'Từ ngày'); ?></label>
                    <input type="date" id="date_from" name="date_from" class="form-control" 
                           value="<?php echo $dateFrom; ?>" required>
                </div>
                <div class="col-md-3">
                    <label for="date_to" class="form-label"><?php echo __('to_date', 'Đến ngày'); ?></label>
                    <input type="date" id="date_to" name="date_to" class="form-control" 
                           value="<?php echo $dateTo; ?>" required>
                </div>
                <div class="col-md-3">
                    <label for="report_type" class="form-label"><?php echo __('report_type', 'Loại báo cáo'); ?></label>
                    <select id="report_type" name="report_type" class="form-select">
                        <option value="overview" <?php echo $reportType === 'overview' ? 'selected' : ''; ?>><?php echo __('overview_report', 'Tổng quan'); ?></option>
                        <option value="revenue" <?php echo $reportType === 'revenue' ? 'selected' : ''; ?>><?php echo __('revenue_report', 'Doanh thu'); ?></option>
                        <option value="items" <?php echo $reportType === 'items' ? 'selected' : ''; ?>><?php echo __('items_report', 'Món ăn'); ?></option>
                        <option value="tables" <?php echo $reportType === 'tables' ? 'selected' : ''; ?>><?php echo __('tables_report', 'Bàn'); ?></option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">&nbsp;</label>
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-chart-line me-2"></i><?php echo __('generate_report', 'Tạo báo cáo'); ?>
                    </button>
                </div>
            </form>
        </div>
        
        <!-- Key Metrics -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="metric-card">
                    <div class="metric-value"><?php echo formatCurrency($revenueReport['total_revenue'] ?? 0); ?></div>
                    <div class="metric-label"><?php echo __('total_revenue', 'Tổng doanh thu'); ?></div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card">
                    <div class="metric-value"><?php echo $revenueReport['total_orders'] ?? 0; ?></div>
                    <div class="metric-label"><?php echo __('total_orders', 'Tổng đơn hàng'); ?></div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card">
                    <div class="metric-value"><?php echo formatCurrency($revenueReport['avg_order_value'] ?? 0); ?></div>
                    <div class="metric-label"><?php echo __('avg_order_value', 'Giá trị đơn hàng TB'); ?></div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card">
                    <div class="metric-value"><?php echo number_format($revenueReport['table_turnover'] ?? 0, 1); ?></div>
                    <div class="metric-label"><?php echo __('table_turnover', 'Tỷ lệ luân chuyển bàn'); ?></div>
                </div>
            </div>
        </div>
        
        <!-- Charts Section -->
        <div class="row">
            <!-- Daily Revenue Chart -->
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-line me-2"></i><?php echo __('daily_revenue_chart', 'Biểu đồ doanh thu theo ngày'); ?>
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="revenueChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Payment Methods Chart -->
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-credit-card me-2"></i><?php echo __('payment_methods', 'Phương thức thanh toán'); ?>
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="paymentChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Popular Items -->
        <div class="row mt-4">
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-star me-2"></i><?php echo __('popular_items', 'Món ăn phổ biến'); ?>
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($popularItems)): ?>
                            <p class="text-muted text-center py-4">
                                <?php echo __('no_data_available', 'Không có dữ liệu'); ?>
                            </p>
                        <?php else: ?>
                            <?php foreach ($popularItems as $index => $item): ?>
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <div class="d-flex align-items-center">
                                        <span class="badge bg-primary me-3">#<?php echo $index + 1; ?></span>
                                        <div>
                                            <strong><?php echo htmlspecialchars($item['food_name']); ?></strong>
                                            <br><small class="text-muted"><?php echo $item['total_quantity']; ?> <?php echo __('orders', 'lần gọi'); ?></small>
                                        </div>
                                    </div>
                                    <div class="text-end">
                                        <strong class="text-success"><?php echo formatCurrency($item['total_revenue']); ?></strong>
                                        <div class="progress progress-custom mt-1" style="width: 100px;">
                                            <div class="progress-bar" style="width: <?php echo ($item['total_quantity'] / $popularItems[0]['total_quantity']) * 100; ?>%"></div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <!-- Staff Performance -->
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-users me-2"></i><?php echo __('staff_performance', 'Hiệu suất nhân viên'); ?>
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($staffPerformance)): ?>
                            <p class="text-muted text-center py-4">
                                <?php echo __('no_data_available', 'Không có dữ liệu'); ?>
                            </p>
                        <?php else: ?>
                            <?php foreach ($staffPerformance as $staff): ?>
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <div>
                                        <strong><?php echo htmlspecialchars($staff['staff_name']); ?></strong>
                                        <br><small class="text-muted"><?php echo $staff['total_orders']; ?> <?php echo __('orders', 'đơn hàng'); ?></small>
                                    </div>
                                    <div class="text-end">
                                        <strong class="text-primary"><?php echo formatCurrency($staff['total_revenue']); ?></strong>
                                        <div class="progress progress-custom mt-1" style="width: 100px;">
                                            <div class="progress-bar bg-info" style="width: <?php echo ($staff['total_revenue'] / $staffPerformance[0]['total_revenue']) * 100; ?>%"></div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Table Utilization -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chair me-2"></i><?php echo __('table_utilization', 'Tỷ lệ sử dụng bàn'); ?>
                </h5>
            </div>
            <div class="card-body">
                <div class="table-utilization">
                    <?php foreach ($tableUtilization as $table): ?>
                        <div class="table-card">
                            <h6><?php echo __('table', 'Bàn'); ?> <?php echo htmlspecialchars($table['table_number']); ?></h6>
                            <div class="metric-value" style="font-size: 1.5rem;"><?php echo number_format($table['utilization_rate'], 1); ?>%</div>
                            <div class="metric-label"><?php echo $table['total_orders']; ?> <?php echo __('orders', 'đơn hàng'); ?></div>
                            <div class="progress progress-custom mt-2">
                                <div class="progress-bar bg-success" style="width: <?php echo $table['utilization_rate']; ?>%"></div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>
    
    <!-- MDBootstrap JS -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/mdb-ui-kit/6.4.2/mdb.min.js"></script>
    <!-- Custom JS -->
    <script src="../../assets/js/app.js"></script>
    
    <script>
        // Revenue Chart
        const revenueCtx = document.getElementById('revenueChart').getContext('2d');
        const revenueChart = new Chart(revenueCtx, {
            type: 'line',
            data: {
                labels: [<?php echo "'" . implode("','", array_column($dailyRevenue, 'date')) . "'"; ?>],
                datasets: [{
                    label: '<?php echo __('daily_revenue', 'Doanh thu hàng ngày'); ?>',
                    data: [<?php echo implode(',', array_column($dailyRevenue, 'revenue')); ?>],
                    borderColor: '#007bff',
                    backgroundColor: 'rgba(0, 123, 255, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return new Intl.NumberFormat('vi-VN').format(value) + ' ₫';
                            }
                        }
                    }
                },
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return context.dataset.label + ': ' + new Intl.NumberFormat('vi-VN').format(context.parsed.y) + ' ₫';
                            }
                        }
                    }
                }
            }
        });
        
        // Payment Methods Chart
        const paymentCtx = document.getElementById('paymentChart').getContext('2d');
        const paymentChart = new Chart(paymentCtx, {
            type: 'doughnut',
            data: {
                labels: [<?php echo "'" . implode("','", array_column($paymentMethods, 'payment_method')) . "'"; ?>],
                datasets: [{
                    data: [<?php echo implode(',', array_column($paymentMethods, 'total_amount')); ?>],
                    backgroundColor: ['#007bff', '#28a745', '#ffc107', '#dc3545', '#6f42c1']
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return context.label + ': ' + new Intl.NumberFormat('vi-VN').format(context.parsed) + ' ₫';
                            }
                        }
                    }
                }
            }
        });
        
        // Export functions
        function exportToExcel() {
            window.open('export.php?type=excel&date_from=<?php echo $dateFrom; ?>&date_to=<?php echo $dateTo; ?>', '_blank');
        }
        
        function exportToPDF() {
            window.open('export.php?type=pdf&date_from=<?php echo $dateFrom; ?>&date_to=<?php echo $dateTo; ?>', '_blank');
        }
        
        function printReport() {
            window.print();
        }
    </script>
</body>
</html>
