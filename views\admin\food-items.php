<?php
/**
 * Food Items Management Page
 * 
 * Admin interface for managing food items including CRUD operations,
 * search functionality, and food item statistics.
 */

require_once '../../config/config.php';
require_once '../../config/database.php';
require_once '../../includes/functions.php';
require_once '../../models/FoodItem.php';
require_once '../../models/Category.php';

// Check admin access
requireAdmin();

// Initialize models
$foodItemModel = new FoodItem();
$categoryModel = new Category();

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
        setFlashMessage('error', __('invalid_csrf_token', 'Token bảo mật không hợp lệ. Vui lòng thử lại.'));
    } else {
        $action = $_POST['action'] ?? '';
        
        switch ($action) {
            case 'create':
                $foodData = [
                    'food_name' => sanitizeInput($_POST['food_name'] ?? ''),
                    'category_id' => (int)($_POST['category_id'] ?? 0),
                    'price' => (float)($_POST['price'] ?? 0),
                    'description' => sanitizeInput($_POST['description'] ?? ''),
                    'image_path' => sanitizeInput($_POST['image_path'] ?? ''),
                    'status' => (int)($_POST['status'] ?? 1)
                ];
                
                if (empty($foodData['food_name']) || $foodData['category_id'] <= 0 || $foodData['price'] <= 0) {
                    setFlashMessage('error', __('food_required_fields', 'Tên món ăn, danh mục và giá là bắt buộc.'));
                } else {
                    $foodId = $foodItemModel->createFoodItem($foodData);
                    if ($foodId) {
                        setFlashMessage('success', __('food_created_success', 'Tạo món ăn thành công.'));
                        logActivity("Tạo món ăn: {$foodData['food_name']}", $_SESSION['user_id']);
                    } else {
                        setFlashMessage('error', __('food_create_failed', 'Tạo món ăn thất bại. Tên món ăn có thể đã tồn tại trong danh mục này.'));
                    }
                }
                break;
                
            case 'update':
                $foodId = (int)($_POST['food_id'] ?? 0);
                $foodData = [
                    'food_name' => sanitizeInput($_POST['food_name'] ?? ''),
                    'category_id' => (int)($_POST['category_id'] ?? 0),
                    'price' => (float)($_POST['price'] ?? 0),
                    'description' => sanitizeInput($_POST['description'] ?? ''),
                    'image_path' => sanitizeInput($_POST['image_path'] ?? ''),
                    'status' => (int)($_POST['status'] ?? 1)
                ];
                
                if (empty($foodData['food_name']) || $foodData['category_id'] <= 0 || $foodData['price'] <= 0) {
                    setFlashMessage('error', __('food_required_fields', 'Tên món ăn, danh mục và giá là bắt buộc.'));
                } else {
                    if ($foodItemModel->updateFoodItem($foodId, $foodData)) {
                        setFlashMessage('success', __('food_updated_success', 'Cập nhật món ăn thành công.'));
                        logActivity("Cập nhật món ăn ID: $foodId", $_SESSION['user_id']);
                    } else {
                        setFlashMessage('error', __('food_update_failed', 'Cập nhật món ăn thất bại. Tên món ăn có thể đã tồn tại trong danh mục này.'));
                    }
                }
                break;
                
            case 'delete':
                $foodId = (int)($_POST['food_id'] ?? 0);
                if ($foodItemModel->deleteFoodItem($foodId)) {
                    setFlashMessage('success', __('food_deleted_success', 'Xóa món ăn thành công.'));
                    logActivity("Xóa món ăn ID: $foodId", $_SESSION['user_id']);
                } else {
                    setFlashMessage('error', __('food_delete_failed', 'Xóa món ăn thất bại. Món ăn có thể có đơn hàng liên quan.'));
                }
                break;
        }
        
        // Redirect to prevent form resubmission
        header('Location: food-items.php');
        exit();
    }
}

// Handle GET requests for editing
$editFoodItem = null;
if (isset($_GET['edit'])) {
    $editId = (int)$_GET['edit'];
    $editFoodItem = $foodItemModel->getFoodItemById($editId);
}

// Get food items with pagination and search
$page = (int)($_GET['page'] ?? 1);
$search = sanitizeInput($_GET['search'] ?? '');
$categoryFilter = (int)($_GET['category'] ?? 0);

if ($search || $categoryFilter) {
    $foodItemsData = $foodItemModel->searchFoodItems($search, $page, ITEMS_PER_PAGE, $categoryFilter);
} else {
    $foodItemsData = $foodItemModel->getAllFoodItems($page, ITEMS_PER_PAGE, false, $categoryFilter);
}

// Get categories for dropdown
$activeCategories = $categoryModel->getActiveCategories();

$pageTitle = __('food_items_management', 'Quản lý món ăn') . ' - ' . APP_NAME;
?>
<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?></title>
    
    <!-- MDBootstrap CSS -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/mdb-ui-kit/6.4.2/mdb.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="../../assets/css/custom.css" rel="stylesheet">
</head>
<body>
    <!-- Sidebar -->
    <nav class="sidebar">
        <div class="p-3">
            <div class="text-center mb-4">
                <i class="fas fa-utensils fa-2x text-white mb-2"></i>
                <h5 class="text-white mb-0"><?php echo APP_NAME; ?></h5>
                <small class="text-white-50"><?php echo __('admin', 'Quản trị viên'); ?></small>
            </div>
            
            <div class="mb-3">
                <small class="text-white-50 text-uppercase"><?php echo __('dashboard_welcome', 'Chào mừng'); ?></small>
                <div class="text-white">
                    <i class="fas fa-user-shield me-2"></i>
                    <?php echo htmlspecialchars($_SESSION['full_name']); ?>
                </div>
            </div>
            
            <hr class="text-white-50">
            
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link" href="dashboard.php">
                        <i class="fas fa-tachometer-alt me-2"></i><?php echo __('dashboard', 'Bảng điều khiển'); ?>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="categories.php">
                        <i class="fas fa-tags me-2"></i><?php echo __('categories', 'Danh mục'); ?>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link active" href="food-items.php">
                        <i class="fas fa-hamburger me-2"></i><?php echo __('food_items', 'Món ăn'); ?>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="tables.php">
                        <i class="fas fa-chair me-2"></i><?php echo __('tables', 'Bàn'); ?>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="users.php">
                        <i class="fas fa-users me-2"></i><?php echo __('users', 'Người dùng'); ?>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="orders.php">
                        <i class="fas fa-receipt me-2"></i><?php echo __('orders', 'Đơn hàng'); ?>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="reports.php">
                        <i class="fas fa-chart-bar me-2"></i><?php echo __('reports', 'Báo cáo'); ?>
                    </a>
                </li>
            </ul>
            
            <hr class="text-white-50">
            
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link" href="../auth/logout.php">
                        <i class="fas fa-sign-out-alt me-2"></i><?php echo __('logout', 'Đăng xuất'); ?>
                    </a>
                </li>
            </ul>
        </div>
    </nav>
    
    <!-- Main Content -->
    <div class="main-content">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2><i class="fas fa-hamburger me-2"></i><?php echo __('food_items_management', 'Quản lý món ăn'); ?></h2>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#foodItemModal">
                <i class="fas fa-plus me-2"></i><?php echo __('add_food_item', 'Thêm món ăn'); ?>
            </button>
        </div>
        
        <?php displayFlashMessage(); ?>
        
        <!-- Search and Filter -->
        <div class="card mb-4">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-5">
                        <div class="form-outline">
                            <input type="text" id="search" name="search" class="form-control" 
                                   value="<?php echo htmlspecialchars($search); ?>">
                            <label class="form-label" for="search"><?php echo __('search_food_items', 'Tìm kiếm món ăn...'); ?></label>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <select id="category" name="category" class="form-select">
                            <option value=""><?php echo __('all_categories', 'Tất cả danh mục'); ?></option>
                            <?php foreach ($activeCategories as $category): ?>
                                <option value="<?php echo $category['category_id']; ?>" 
                                        <?php echo $categoryFilter == $category['category_id'] ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($category['category_name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <button type="submit" class="btn btn-outline-primary me-2">
                            <i class="fas fa-search me-1"></i><?php echo __('search', 'Tìm kiếm'); ?>
                        </button>
                        <a href="food-items.php" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-1"></i><?php echo __('clear', 'Xóa'); ?>
                        </a>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Food Items Table -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><?php echo __('food_items_list', 'Danh sách món ăn'); ?></h5>
                <span class="badge bg-primary">
                    <?php echo __('total', 'Tổng cộng'); ?>: <?php echo $foodItemsData['total'] ?? 0; ?>
                </span>
            </div>
            <div class="card-body">
                <?php if (empty($foodItemsData['food_items'])): ?>
                    <div class="text-center py-4">
                        <i class="fas fa-hamburger fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted"><?php echo __('no_food_items_found', 'Không tìm thấy món ăn'); ?></h5>
                        <p class="text-muted">
                            <?php echo ($search || $categoryFilter) ? __('adjust_search_criteria', 'Thử điều chỉnh tiêu chí tìm kiếm.') : __('add_first_food_item', 'Bắt đầu bằng cách thêm món ăn đầu tiên.'); ?>
                        </p>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th><?php echo __('food_name', 'Tên món ăn'); ?></th>
                                    <th><?php echo __('category', 'Danh mục'); ?></th>
                                    <th><?php echo __('price', 'Giá'); ?></th>
                                    <th><?php echo __('status', 'Trạng thái'); ?></th>
                                    <th><?php echo __('created_at', 'Ngày tạo'); ?></th>
                                    <th><?php echo __('actions', 'Thao tác'); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($foodItemsData['food_items'] as $foodItem): ?>
                                    <tr>
                                        <td><?php echo $foodItem['food_id']; ?></td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <?php if ($foodItem['image_path']): ?>
                                                    <img src="../../<?php echo htmlspecialchars($foodItem['image_path']); ?>" 
                                                         alt="<?php echo htmlspecialchars($foodItem['food_name']); ?>"
                                                         class="rounded me-2" style="width: 40px; height: 40px; object-fit: cover;">
                                                <?php else: ?>
                                                    <div class="bg-light rounded me-2 d-flex align-items-center justify-content-center" 
                                                         style="width: 40px; height: 40px;">
                                                        <i class="fas fa-image text-muted"></i>
                                                    </div>
                                                <?php endif; ?>
                                                <div>
                                                    <strong><?php echo htmlspecialchars($foodItem['food_name']); ?></strong>
                                                    <?php if ($foodItem['description']): ?>
                                                        <br><small class="text-muted"><?php echo truncateText(htmlspecialchars($foodItem['description']), 30); ?></small>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </td>
                                        <td><?php echo htmlspecialchars($foodItem['category_name']); ?></td>
                                        <td><strong><?php echo formatCurrency($foodItem['price']); ?></strong></td>
                                        <td>
                                            <span class="badge bg-<?php echo $foodItem['status'] ? 'success' : 'danger'; ?>">
                                                <?php echo $foodItem['status'] ? __('food_available', 'Còn món') : __('food_unavailable', 'Hết món'); ?>
                                            </span>
                                        </td>
                                        <td><?php echo formatDate($foodItem['created_at']); ?></td>
                                        <td>
                                            <a href="?edit=<?php echo $foodItem['food_id']; ?>" 
                                               class="btn btn-sm btn-outline-primary btn-action me-1"
                                               data-bs-toggle="tooltip" title="<?php echo __('edit', 'Sửa'); ?>">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" 
                                                    class="btn btn-sm btn-outline-danger btn-action"
                                                    data-bs-toggle="tooltip" title="<?php echo __('delete', 'Xóa'); ?>"
                                                    onclick="confirmDelete(<?php echo $foodItem['food_id']; ?>, '<?php echo htmlspecialchars($foodItem['food_name']); ?>')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    <?php if ($foodItemsData['pages'] > 1): ?>
                        <div class="mt-3">
                            <?php 
                            $params = [];
                            if ($search) $params['search'] = $search;
                            if ($categoryFilter) $params['category'] = $categoryFilter;
                            echo generatePagination($foodItemsData['current_page'], $foodItemsData['pages'], 'food-items.php', $params); 
                            ?>
                        </div>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- Food Item Modal -->
    <div class="modal fade" id="foodItemModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-hamburger me-2"></i>
                        <?php echo $editFoodItem ? __('edit_food_item', 'Sửa món ăn') : __('add_new_food_item', 'Thêm món ăn mới'); ?>
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                        <input type="hidden" name="action" value="<?php echo $editFoodItem ? 'update' : 'create'; ?>">
                        <?php if ($editFoodItem): ?>
                            <input type="hidden" name="food_id" value="<?php echo $editFoodItem['food_id']; ?>">
                        <?php endif; ?>
                        
                        <div class="row">
                            <div class="col-md-8">
                                <div class="form-outline mb-3">
                                    <input type="text" id="food_name" name="food_name" class="form-control" 
                                           value="<?php echo $editFoodItem ? htmlspecialchars($editFoodItem['food_name']) : ''; ?>" required>
                                    <label class="form-label" for="food_name"><?php echo __('food_name', 'Tên món ăn'); ?> *</label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-outline mb-3">
                                    <input type="number" id="price" name="price" class="form-control" step="1000" min="0"
                                           value="<?php echo $editFoodItem ? $editFoodItem['price'] : ''; ?>" required>
                                    <label class="form-label" for="price"><?php echo __('price', 'Giá'); ?> (VND) *</label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="category_id" class="form-label"><?php echo __('category', 'Danh mục'); ?> *</label>
                                    <select id="category_id" name="category_id" class="form-select" required>
                                        <option value=""><?php echo __('select_category', 'Chọn danh mục'); ?></option>
                                        <?php foreach ($activeCategories as $category): ?>
                                            <option value="<?php echo $category['category_id']; ?>" 
                                                    <?php echo ($editFoodItem && $editFoodItem['category_id'] == $category['category_id']) ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($category['category_name']); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-outline mb-3">
                                    <input type="text" id="image_path" name="image_path" class="form-control" 
                                           value="<?php echo $editFoodItem ? htmlspecialchars($editFoodItem['image_path']) : ''; ?>">
                                    <label class="form-label" for="image_path"><?php echo __('image_path', 'Đường dẫn hình ảnh'); ?></label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-outline mb-3">
                            <textarea id="description" name="description" class="form-control" rows="3"><?php echo $editFoodItem ? htmlspecialchars($editFoodItem['description']) : ''; ?></textarea>
                            <label class="form-label" for="description"><?php echo __('description', 'Mô tả'); ?></label>
                        </div>
                        
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="status" name="status" value="1" 
                                   <?php echo (!$editFoodItem || $editFoodItem['status']) ? 'checked' : ''; ?>>
                            <label class="form-check-label" for="status"><?php echo __('food_available', 'Còn món'); ?></label>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?php echo __('cancel', 'Hủy'); ?></button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            <?php echo $editFoodItem ? __('update', 'Cập nhật') : __('create', 'Tạo mới'); ?>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-exclamation-triangle me-2 text-danger"></i>
                        <?php echo __('confirm_delete', 'Xác nhận xóa'); ?>
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p><?php echo __('confirm_delete_food_item', 'Bạn có chắc chắn muốn xóa món ăn'); ?> "<span id="deleteFoodItemName"></span>"?</p>
                    <p class="text-danger small">
                        <i class="fas fa-warning me-1"></i>
                        <?php echo __('action_cannot_undone_food', 'Hành động này không thể hoàn tác. Món ăn chỉ có thể xóa nếu không có đơn hàng liên quan.'); ?>
                    </p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?php echo __('cancel', 'Hủy'); ?></button>
                    <form method="POST" style="display: inline;">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                        <input type="hidden" name="action" value="delete">
                        <input type="hidden" name="food_id" id="deleteFoodItemId">
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-trash me-2"></i><?php echo __('delete', 'Xóa'); ?>
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <!-- MDBootstrap JS -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/mdb-ui-kit/6.4.2/mdb.min.js"></script>
    <!-- Custom JS -->
    <script src="../../assets/js/app.js"></script>
    
    <script>
        // Show modal if editing
        <?php if ($editFoodItem): ?>
        document.addEventListener('DOMContentLoaded', function() {
            const modal = new mdb.Modal(document.getElementById('foodItemModal'));
            modal.show();
        });
        <?php endif; ?>
        
        // Delete confirmation
        function confirmDelete(foodItemId, foodItemName) {
            document.getElementById('deleteFoodItemId').value = foodItemId;
            document.getElementById('deleteFoodItemName').textContent = foodItemName;
            const modal = new mdb.Modal(document.getElementById('deleteModal'));
            modal.show();
        }
        
        // Initialize tooltips
        document.addEventListener('DOMContentLoaded', function() {
            const tooltips = document.querySelectorAll('[data-bs-toggle="tooltip"]');
            tooltips.forEach(tooltip => {
                new mdb.Tooltip(tooltip);
            });
        });
    </script>
</body>
</html>
