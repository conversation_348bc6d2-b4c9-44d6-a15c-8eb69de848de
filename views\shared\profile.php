<?php
/**
 * Profile Management Page
 * 
 * User profile management interface for both admin and staff roles
 * with password change, personal information update, and activity history.
 */

require_once '../../config/config.php';
require_once '../../config/database.php';
require_once '../../includes/functions.php';
require_once '../../models/User.php';

// Check login
requireLogin();

// Initialize user model
$userModel = new User();

// Get current user details
$currentUser = $userModel->getUserById($_SESSION['user_id']);
if (!$currentUser) {
    setFlashMessage('error', __('user_not_found', 'Không tìm thấy người dùng.'));
    header('Location: ../auth/logout.php');
    exit();
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
        setFlashMessage('error', __('invalid_csrf_token', 'Token bảo mật không hợp lệ. Vui lòng thử lại.'));
    } else {
        $action = $_POST['action'] ?? '';
        
        switch ($action) {
            case 'update_profile':
                $profileData = [
                    'full_name' => sanitizeInput($_POST['full_name'] ?? ''),
                    'email' => sanitizeInput($_POST['email'] ?? ''),
                    'phone' => sanitizeInput($_POST['phone'] ?? '')
                ];
                
                // Validation
                $errors = [];
                if (empty($profileData['full_name'])) {
                    $errors[] = __('full_name_required', 'Họ và tên là bắt buộc.');
                }
                if (empty($profileData['email']) || !filter_var($profileData['email'], FILTER_VALIDATE_EMAIL)) {
                    $errors[] = __('email_invalid', 'Email không hợp lệ.');
                }
                
                if (empty($errors)) {
                    if ($userModel->updateUserProfile($_SESSION['user_id'], $profileData)) {
                        // Update session data
                        $_SESSION['full_name'] = $profileData['full_name'];
                        $_SESSION['email'] = $profileData['email'];
                        
                        setFlashMessage('success', __('profile_updated_success', 'Cập nhật hồ sơ thành công.'));
                        logActivity("Cập nhật hồ sơ cá nhân", $_SESSION['user_id']);
                    } else {
                        setFlashMessage('error', __('profile_update_failed', 'Cập nhật hồ sơ thất bại. Email có thể đã được sử dụng.'));
                    }
                } else {
                    setFlashMessage('error', implode('<br>', $errors));
                }
                break;
                
            case 'change_password':
                $currentPassword = $_POST['current_password'] ?? '';
                $newPassword = $_POST['new_password'] ?? '';
                $confirmPassword = $_POST['confirm_password'] ?? '';
                
                // Validation
                $errors = [];
                if (empty($currentPassword)) {
                    $errors[] = __('current_password_required', 'Mật khẩu hiện tại là bắt buộc.');
                }
                if (empty($newPassword)) {
                    $errors[] = __('new_password_required', 'Mật khẩu mới là bắt buộc.');
                }
                if (strlen($newPassword) < 6) {
                    $errors[] = __('password_min_length', 'Mật khẩu phải có ít nhất 6 ký tự.');
                }
                if ($newPassword !== $confirmPassword) {
                    $errors[] = __('password_mismatch', 'Mật khẩu xác nhận không khớp.');
                }
                
                // Verify current password
                if (empty($errors) && !password_verify($currentPassword, $currentUser['password'])) {
                    $errors[] = __('current_password_incorrect', 'Mật khẩu hiện tại không đúng.');
                }
                
                if (empty($errors)) {
                    if ($userModel->changePassword($_SESSION['user_id'], $newPassword)) {
                        setFlashMessage('success', __('password_changed_success', 'Đổi mật khẩu thành công.'));
                        logActivity("Đổi mật khẩu", $_SESSION['user_id']);
                    } else {
                        setFlashMessage('error', __('password_change_failed', 'Đổi mật khẩu thất bại.'));
                    }
                } else {
                    setFlashMessage('error', implode('<br>', $errors));
                }
                break;
        }
        
        // Redirect to prevent form resubmission
        header('Location: profile.php');
        exit();
    }
}

// Get user activity history
$activityHistory = $userModel->getUserActivityHistory($_SESSION['user_id'], 1, 10);

// Determine dashboard URL based on role
$dashboardUrl = ($_SESSION['role'] === 'admin') ? '../admin/dashboard.php' : '../staff/dashboard.php';

$pageTitle = __('profile_management', 'Quản lý hồ sơ') . ' - ' . APP_NAME;
?>
<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?></title>
    
    <!-- MDBootstrap CSS -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/mdb-ui-kit/6.4.2/mdb.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="../../assets/css/custom.css" rel="stylesheet">
    
    <style>
        .profile-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
        }
        .profile-avatar {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            margin-bottom: 20px;
        }
        .profile-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .activity-item {
            border-left: 3px solid #007bff;
            padding: 15px;
            margin-bottom: 15px;
            background: white;
            border-radius: 0 10px 10px 0;
        }
        .role-badge {
            background: rgba(255, 255, 255, 0.2);
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="<?php echo $dashboardUrl; ?>">
                <i class="fas fa-utensils me-2"></i><?php echo APP_NAME; ?>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo $dashboardUrl; ?>">
                            <i class="fas fa-tachometer-alt me-1"></i><?php echo __('dashboard', 'Bảng điều khiển'); ?>
                        </a>
                    </li>
                    <?php if ($_SESSION['role'] === 'admin'): ?>
                        <li class="nav-item">
                            <a class="nav-link" href="../admin/users.php">
                                <i class="fas fa-users me-1"></i><?php echo __('users', 'Người dùng'); ?>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../admin/reports.php">
                                <i class="fas fa-chart-bar me-1"></i><?php echo __('reports', 'Báo cáo'); ?>
                            </a>
                        </li>
                    <?php else: ?>
                        <li class="nav-item">
                            <a class="nav-link" href="../staff/orders.php">
                                <i class="fas fa-receipt me-1"></i><?php echo __('orders', 'Đơn hàng'); ?>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../staff/new-order.php">
                                <i class="fas fa-plus me-1"></i><?php echo __('new_order', 'Đơn hàng mới'); ?>
                            </a>
                        </li>
                    <?php endif; ?>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle active" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle me-1"></i>
                            <?php echo htmlspecialchars($_SESSION['full_name']); ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item active" href="profile.php">
                                <i class="fas fa-user me-2"></i><?php echo __('profile', 'Hồ sơ'); ?>
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../auth/logout.php">
                                <i class="fas fa-sign-out-alt me-2"></i><?php echo __('logout', 'Đăng xuất'); ?>
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    
    <!-- Main Content -->
    <div class="container-fluid mt-4">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <!-- Profile Header -->
                <div class="profile-header text-center">
                    <div class="profile-avatar mx-auto">
                        <i class="fas fa-user"></i>
                    </div>
                    <h2><?php echo htmlspecialchars($currentUser['full_name']); ?></h2>
                    <p class="mb-2">
                        <span class="role-badge">
                            <i class="fas fa-<?php echo $_SESSION['role'] === 'admin' ? 'user-shield' : 'user'; ?> me-2"></i>
                            <?php echo $_SESSION['role'] === 'admin' ? __('admin', 'Quản trị viên') : __('staff', 'Nhân viên'); ?>
                        </span>
                    </p>
                    <p class="mb-0">
                        <i class="fas fa-envelope me-2"></i><?php echo htmlspecialchars($currentUser['email']); ?>
                        <?php if ($currentUser['phone']): ?>
                            <span class="ms-3"><i class="fas fa-phone me-2"></i><?php echo htmlspecialchars($currentUser['phone']); ?></span>
                        <?php endif; ?>
                    </p>
                    <small><?php echo __('member_since', 'Thành viên từ'); ?> <?php echo formatVietnameseDate($currentUser['created_at']); ?></small>
                </div>
                
                <?php displayFlashMessage(); ?>
                
                <div class="row">
                    <!-- Profile Information -->
                    <div class="col-lg-6">
                        <div class="profile-section">
                            <h5 class="mb-4">
                                <i class="fas fa-user-edit me-2"></i><?php echo __('personal_information', 'Thông tin cá nhân'); ?>
                            </h5>
                            
                            <form method="POST">
                                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                <input type="hidden" name="action" value="update_profile">
                                
                                <div class="form-outline mb-3">
                                    <input type="text" id="full_name" name="full_name" class="form-control" 
                                           value="<?php echo htmlspecialchars($currentUser['full_name']); ?>" required>
                                    <label class="form-label" for="full_name"><?php echo __('full_name', 'Họ và tên'); ?> *</label>
                                </div>
                                
                                <div class="form-outline mb-3">
                                    <input type="email" id="email" name="email" class="form-control" 
                                           value="<?php echo htmlspecialchars($currentUser['email']); ?>" required>
                                    <label class="form-label" for="email"><?php echo __('email', 'Email'); ?> *</label>
                                </div>
                                
                                <div class="form-outline mb-3">
                                    <input type="tel" id="phone" name="phone" class="form-control" 
                                           value="<?php echo htmlspecialchars($currentUser['phone'] ?? ''); ?>">
                                    <label class="form-label" for="phone"><?php echo __('phone', 'Số điện thoại'); ?></label>
                                </div>
                                
                                <div class="form-outline mb-3">
                                    <input type="text" id="username" class="form-control" 
                                           value="<?php echo htmlspecialchars($currentUser['username']); ?>" readonly>
                                    <label class="form-label" for="username"><?php echo __('username', 'Tên đăng nhập'); ?></label>
                                    <div class="form-text"><?php echo __('username_readonly', 'Tên đăng nhập không thể thay đổi.'); ?></div>
                                </div>
                                
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i><?php echo __('update_profile', 'Cập nhật hồ sơ'); ?>
                                </button>
                            </form>
                        </div>
                    </div>
                    
                    <!-- Change Password -->
                    <div class="col-lg-6">
                        <div class="profile-section">
                            <h5 class="mb-4">
                                <i class="fas fa-lock me-2"></i><?php echo __('change_password', 'Đổi mật khẩu'); ?>
                            </h5>
                            
                            <form method="POST" id="passwordForm">
                                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                <input type="hidden" name="action" value="change_password">
                                
                                <div class="form-outline mb-3">
                                    <input type="password" id="current_password" name="current_password" class="form-control" required>
                                    <label class="form-label" for="current_password"><?php echo __('current_password', 'Mật khẩu hiện tại'); ?> *</label>
                                </div>
                                
                                <div class="form-outline mb-3">
                                    <input type="password" id="new_password" name="new_password" class="form-control" 
                                           minlength="6" required>
                                    <label class="form-label" for="new_password"><?php echo __('new_password', 'Mật khẩu mới'); ?> *</label>
                                    <div class="form-text"><?php echo __('password_requirements', 'Mật khẩu phải có ít nhất 6 ký tự.'); ?></div>
                                </div>
                                
                                <div class="form-outline mb-3">
                                    <input type="password" id="confirm_password" name="confirm_password" class="form-control" 
                                           minlength="6" required>
                                    <label class="form-label" for="confirm_password"><?php echo __('confirm_password', 'Xác nhận mật khẩu'); ?> *</label>
                                </div>
                                
                                <button type="submit" class="btn btn-warning">
                                    <i class="fas fa-key me-2"></i><?php echo __('change_password', 'Đổi mật khẩu'); ?>
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
                
                <!-- Activity History -->
                <div class="profile-section">
                    <h5 class="mb-4">
                        <i class="fas fa-history me-2"></i><?php echo __('recent_activity', 'Hoạt động gần đây'); ?>
                    </h5>
                    
                    <?php if (empty($activityHistory['activities'])): ?>
                        <p class="text-muted text-center py-4">
                            <?php echo __('no_recent_activity', 'Không có hoạt động gần đây.'); ?>
                        </p>
                    <?php else: ?>
                        <?php foreach ($activityHistory['activities'] as $activity): ?>
                            <div class="activity-item">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <strong><?php echo htmlspecialchars($activity['activity']); ?></strong>
                                        <br><small class="text-muted">
                                            <i class="fas fa-clock me-1"></i>
                                            <?php echo formatVietnameseDate($activity['created_at']); ?>
                                        </small>
                                    </div>
                                    <span class="badge bg-primary">
                                        <i class="fas fa-user me-1"></i><?php echo htmlspecialchars($activity['user_name']); ?>
                                    </span>
                                </div>
                            </div>
                        <?php endforeach; ?>
                        
                        <?php if ($activityHistory['total'] > 10): ?>
                            <div class="text-center mt-3">
                                <small class="text-muted">
                                    <?php echo __('showing_recent_activities', 'Hiển thị 10 hoạt động gần đây nhất'); ?>
                                    (<?php echo __('total', 'Tổng cộng'); ?>: <?php echo $activityHistory['total']; ?>)
                                </small>
                            </div>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    
    <!-- MDBootstrap JS -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/mdb-ui-kit/6.4.2/mdb.min.js"></script>
    <!-- Custom JS -->
    <script src="../../assets/js/app.js"></script>
    
    <script>
        // Password confirmation validation
        document.addEventListener('DOMContentLoaded', function() {
            const passwordForm = document.getElementById('passwordForm');
            const newPassword = document.getElementById('new_password');
            const confirmPassword = document.getElementById('confirm_password');
            
            function validatePassword() {
                if (newPassword.value !== confirmPassword.value) {
                    confirmPassword.setCustomValidity('<?php echo __('password_mismatch', 'Mật khẩu xác nhận không khớp.'); ?>');
                } else {
                    confirmPassword.setCustomValidity('');
                }
            }
            
            newPassword.addEventListener('change', validatePassword);
            confirmPassword.addEventListener('keyup', validatePassword);
            
            // Form submission confirmation
            passwordForm.addEventListener('submit', function(e) {
                if (!confirm('<?php echo __('confirm_password_change', 'Bạn có chắc chắn muốn đổi mật khẩu?'); ?>')) {
                    e.preventDefault();
                }
            });
        });
    </script>
</body>
</html>
