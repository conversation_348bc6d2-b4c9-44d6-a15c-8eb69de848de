/**
 * Main JavaScript Application File
 * 
 * Contains common functionality used throughout the restaurant management system
 * including form validation, AJAX helpers, and UI enhancements.
 */

// Application namespace
const RestaurantApp = {
    // Configuration
    config: {
        baseUrl: window.location.origin,
        csrfToken: document.querySelector('meta[name="csrf-token"]')?.getAttribute('content'),
        autoRefreshInterval: 30000, // 30 seconds
        toastDuration: 5000 // 5 seconds
    },

    // Initialize application
    init() {
        this.initializeComponents();
        this.bindEvents();
        this.startAutoRefresh();
        console.log('Restaurant Management System initialized');
    },

    // Initialize MDBootstrap components
    initializeComponents() {
        // Initialize tooltips
        const tooltips = document.querySelectorAll('[data-bs-toggle="tooltip"]');
        tooltips.forEach(tooltip => {
            new mdb.Tooltip(tooltip);
        });

        // Initialize popovers
        const popovers = document.querySelectorAll('[data-bs-toggle="popover"]');
        popovers.forEach(popover => {
            new mdb.Popover(popover);
        });

        // Initialize form validation
        this.initializeFormValidation();

        // Auto-dismiss alerts
        this.autoDismissAlerts();
    },

    // Bind global event listeners
    bindEvents() {
        // Form submission with loading state
        document.addEventListener('submit', (e) => {
            const form = e.target;
            if (form.tagName === 'FORM') {
                this.handleFormSubmission(form);
            }
        });

        // Confirm delete actions
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('confirm-delete')) {
                e.preventDefault();
                this.confirmDelete(e.target);
            }
        });

        // Handle AJAX links
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('ajax-link')) {
                e.preventDefault();
                this.handleAjaxLink(e.target);
            }
        });

        // Mobile sidebar toggle
        const sidebarToggle = document.getElementById('sidebarToggle');
        if (sidebarToggle) {
            sidebarToggle.addEventListener('click', this.toggleSidebar);
        }
    },

    // Initialize form validation
    initializeFormValidation() {
        const forms = document.querySelectorAll('form[data-validate="true"]');
        forms.forEach(form => {
            form.addEventListener('submit', (e) => {
                if (!this.validateForm(form)) {
                    e.preventDefault();
                    e.stopPropagation();
                }
                form.classList.add('was-validated');
            });
        });
    },

    // Validate form
    validateForm(form) {
        let isValid = true;
        const requiredFields = form.querySelectorAll('[required]');
        
        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                this.showFieldError(field, 'This field is required');
                isValid = false;
            } else {
                this.clearFieldError(field);
            }
        });

        // Email validation
        const emailFields = form.querySelectorAll('input[type="email"]');
        emailFields.forEach(field => {
            if (field.value && !this.isValidEmail(field.value)) {
                this.showFieldError(field, 'Please enter a valid email address');
                isValid = false;
            }
        });

        // Password confirmation
        const passwordField = form.querySelector('input[name="password"]');
        const confirmPasswordField = form.querySelector('input[name="confirm_password"]');
        if (passwordField && confirmPasswordField) {
            if (passwordField.value !== confirmPasswordField.value) {
                this.showFieldError(confirmPasswordField, 'Passwords do not match');
                isValid = false;
            }
        }

        return isValid;
    },

    // Show field error
    showFieldError(field, message) {
        this.clearFieldError(field);
        field.classList.add('is-invalid');
        
        const errorDiv = document.createElement('div');
        errorDiv.className = 'invalid-feedback';
        errorDiv.textContent = message;
        field.parentNode.appendChild(errorDiv);
    },

    // Clear field error
    clearFieldError(field) {
        field.classList.remove('is-invalid');
        const errorDiv = field.parentNode.querySelector('.invalid-feedback');
        if (errorDiv) {
            errorDiv.remove();
        }
    },

    // Handle form submission
    handleFormSubmission(form) {
        const submitBtn = form.querySelector('button[type="submit"]');
        if (submitBtn) {
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Processing...';
            submitBtn.disabled = true;

            // Re-enable button after 5 seconds (fallback)
            setTimeout(() => {
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            }, 5000);
        }
    },

    // Confirm delete action
    confirmDelete(element) {
        const itemName = element.getAttribute('data-item-name') || 'this item';
        const confirmMessage = `Are you sure you want to delete "${itemName}"? This action cannot be undone.`;
        
        if (confirm(confirmMessage)) {
            // If it's a form, submit it
            const form = element.closest('form');
            if (form) {
                form.submit();
            } else {
                // If it's a link, follow it
                window.location.href = element.href;
            }
        }
    },

    // Handle AJAX links
    async handleAjaxLink(link) {
        const url = link.href;
        const method = link.getAttribute('data-method') || 'GET';
        
        try {
            this.showLoading();
            const response = await this.makeAjaxRequest(url, method);
            
            if (response.success) {
                this.showToast('Success', response.message, 'success');
                // Refresh page or update specific content
                if (response.refresh) {
                    setTimeout(() => location.reload(), 1000);
                }
            } else {
                this.showToast('Error', response.message, 'error');
            }
        } catch (error) {
            this.showToast('Error', 'An unexpected error occurred', 'error');
        } finally {
            this.hideLoading();
        }
    },

    // Make AJAX request
    async makeAjaxRequest(url, method = 'GET', data = null) {
        const options = {
            method: method,
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        };

        if (this.config.csrfToken) {
            options.headers['X-CSRF-Token'] = this.config.csrfToken;
        }

        if (data && method !== 'GET') {
            options.body = JSON.stringify(data);
        }

        const response = await fetch(url, options);
        return await response.json();
    },

    // Show loading overlay
    showLoading() {
        let overlay = document.getElementById('loadingOverlay');
        if (!overlay) {
            overlay = document.createElement('div');
            overlay.id = 'loadingOverlay';
            overlay.className = 'spinner-overlay';
            overlay.innerHTML = `
                <div class="text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <div class="mt-2">Please wait...</div>
                </div>
            `;
            document.body.appendChild(overlay);
        }
        overlay.style.display = 'flex';
    },

    // Hide loading overlay
    hideLoading() {
        const overlay = document.getElementById('loadingOverlay');
        if (overlay) {
            overlay.style.display = 'none';
        }
    },

    // Show toast notification
    showToast(title, message, type = 'info') {
        const toastContainer = this.getToastContainer();
        const toastId = 'toast-' + Date.now();
        
        const toastHtml = `
            <div id="${toastId}" class="toast align-items-center text-white bg-${this.getToastColor(type)} border-0" role="alert">
                <div class="d-flex">
                    <div class="toast-body">
                        <strong>${title}</strong><br>
                        ${message}
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                </div>
            </div>
        `;
        
        toastContainer.insertAdjacentHTML('beforeend', toastHtml);
        
        const toastElement = document.getElementById(toastId);
        const toast = new mdb.Toast(toastElement);
        toast.show();
        
        // Auto remove after duration
        setTimeout(() => {
            if (toastElement) {
                toastElement.remove();
            }
        }, this.config.toastDuration);
    },

    // Get toast container
    getToastContainer() {
        let container = document.getElementById('toastContainer');
        if (!container) {
            container = document.createElement('div');
            container.id = 'toastContainer';
            container.className = 'toast-container position-fixed top-0 end-0 p-3';
            container.style.zIndex = '9999';
            document.body.appendChild(container);
        }
        return container;
    },

    // Get toast color based on type
    getToastColor(type) {
        const colors = {
            success: 'success',
            error: 'danger',
            warning: 'warning',
            info: 'info'
        };
        return colors[type] || 'info';
    },

    // Auto-dismiss alerts
    autoDismissAlerts() {
        const alerts = document.querySelectorAll('.alert[data-auto-dismiss="true"]');
        alerts.forEach(alert => {
            setTimeout(() => {
                const bsAlert = new mdb.Alert(alert);
                bsAlert.close();
            }, this.config.toastDuration);
        });
    },

    // Toggle mobile sidebar
    toggleSidebar() {
        const sidebar = document.querySelector('.sidebar');
        if (sidebar) {
            sidebar.classList.toggle('show');
        }
    },

    // Start auto-refresh for dashboard
    startAutoRefresh() {
        if (document.body.classList.contains('dashboard-page')) {
            setInterval(() => {
                if (document.visibilityState === 'visible') {
                    this.refreshDashboardData();
                }
            }, this.config.autoRefreshInterval);
        }
    },

    // Refresh dashboard data
    async refreshDashboardData() {
        try {
            const response = await this.makeAjaxRequest('/api/dashboard-data.php');
            if (response.success) {
                this.updateDashboardElements(response.data);
            }
        } catch (error) {
            console.error('Failed to refresh dashboard data:', error);
        }
    },

    // Update dashboard elements
    updateDashboardElements(data) {
        // Update statistics
        if (data.stats) {
            Object.keys(data.stats).forEach(key => {
                const element = document.getElementById(`stat-${key}`);
                if (element) {
                    element.textContent = data.stats[key];
                }
            });
        }

        // Update tables status
        if (data.tables) {
            data.tables.forEach(table => {
                const tableCard = document.getElementById(`table-${table.id}`);
                if (tableCard) {
                    tableCard.className = `card table-card table-${table.status}`;
                    // Update other table information
                }
            });
        }
    },

    // Utility functions
    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    },

    formatCurrency(amount) {
        return new Intl.NumberFormat('vi-VN', {
            style: 'currency',
            currency: 'VND'
        }).format(amount);
    },

    formatDate(date, options = {}) {
        const defaultOptions = {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        };
        return new Intl.DateTimeFormat('vi-VN', { ...defaultOptions, ...options }).format(new Date(date));
    },

    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
};

// Initialize application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    RestaurantApp.init();
});

// Export for use in other scripts
window.RestaurantApp = RestaurantApp;
