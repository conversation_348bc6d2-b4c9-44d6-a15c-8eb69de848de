<?php
/**
 * Users Management Page
 *
 * Admin interface for managing user accounts including CRUD operations,
 * role assignments, and user statistics.
 */

require_once '../../config/config.php';
require_once '../../config/database.php';
require_once '../../includes/functions.php';
require_once '../../models/User.php';

// Check admin access
requireAdmin();

// Initialize user model
$userModel = new User();

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
        setFlashMessage('error', __('invalid_csrf_token', 'Token bảo mật không hợp lệ. Vui lòng thử lại.'));
    } else {
        $action = $_POST['action'] ?? '';

        switch ($action) {
            case 'create':
                $userData = [
                    'username' => sanitizeInput($_POST['username'] ?? ''),
                    'password' => $_POST['password'] ?? '',
                    'confirm_password' => $_POST['confirm_password'] ?? '',
                    'email' => sanitizeInput($_POST['email'] ?? ''),
                    'full_name' => sanitizeInput($_POST['full_name'] ?? ''),
                    'phone' => sanitizeInput($_POST['phone'] ?? ''),
                    'role_id' => (int)($_POST['role_id'] ?? 0),
                    'status' => (int)($_POST['status'] ?? 1)
                ];

                // Validation
                $errors = [];
                if (empty($userData['username'])) $errors[] = __('username_required', 'Tên đăng nhập là bắt buộc.');
                if (empty($userData['password'])) $errors[] = __('password_required', 'Mật khẩu là bắt buộc.');
                if ($userData['password'] !== $userData['confirm_password']) $errors[] = __('password_mismatch', 'Mật khẩu xác nhận không khớp.');
                if (empty($userData['email']) || !filter_var($userData['email'], FILTER_VALIDATE_EMAIL)) $errors[] = __('email_invalid', 'Email không hợp lệ.');
                if (empty($userData['full_name'])) $errors[] = __('full_name_required', 'Họ và tên là bắt buộc.');
                if ($userData['role_id'] <= 0) $errors[] = __('role_required', 'Vai trò là bắt buộc.');

                if (empty($errors)) {
                    unset($userData['confirm_password']);
                    $userId = $userModel->createUser($userData);
                    if ($userId) {
                        setFlashMessage('success', __('user_created_success', 'Tạo người dùng thành công.'));
                        logActivity("Tạo người dùng: {$userData['username']}", $_SESSION['user_id']);
                    } else {
                        setFlashMessage('error', __('user_create_failed', 'Tạo người dùng thất bại. Tên đăng nhập hoặc email có thể đã tồn tại.'));
                    }
                } else {
                    setFlashMessage('error', implode('<br>', $errors));
                }
                break;

            case 'update':
                $userId = (int)($_POST['user_id'] ?? 0);
                $userData = [
                    'username' => sanitizeInput($_POST['username'] ?? ''),
                    'email' => sanitizeInput($_POST['email'] ?? ''),
                    'full_name' => sanitizeInput($_POST['full_name'] ?? ''),
                    'phone' => sanitizeInput($_POST['phone'] ?? ''),
                    'role_id' => (int)($_POST['role_id'] ?? 0),
                    'status' => (int)($_POST['status'] ?? 1)
                ];

                // Add password if provided
                if (!empty($_POST['password'])) {
                    if ($_POST['password'] !== $_POST['confirm_password']) {
                        setFlashMessage('error', __('password_mismatch', 'Mật khẩu xác nhận không khớp.'));
                        break;
                    }
                    $userData['password'] = $_POST['password'];
                }

                if ($userModel->updateUser($userId, $userData)) {
                    setFlashMessage('success', __('user_updated_success', 'Cập nhật người dùng thành công.'));
                    logActivity("Cập nhật người dùng ID: $userId", $_SESSION['user_id']);
                } else {
                    setFlashMessage('error', __('user_update_failed', 'Cập nhật người dùng thất bại.'));
                }
                break;

            case 'delete':
                $userId = (int)($_POST['user_id'] ?? 0);
                if ($userId === $_SESSION['user_id']) {
                    setFlashMessage('error', __('cannot_delete_self', 'Không thể xóa tài khoản của chính mình.'));
                } elseif ($userModel->deleteUser($userId)) {
                    setFlashMessage('success', __('user_deleted_success', 'Xóa người dùng thành công.'));
                    logActivity("Xóa người dùng ID: $userId", $_SESSION['user_id']);
                } else {
                    setFlashMessage('error', __('user_delete_failed', 'Xóa người dùng thất bại.'));
                }
                break;
        }

        // Redirect to prevent form resubmission
        header('Location: users.php');
        exit();
    }
}

// Handle GET requests for editing
$editUser = null;
if (isset($_GET['edit'])) {
    $editId = (int)$_GET['edit'];
    $editUser = $userModel->getUserById($editId);
}

// Get users with pagination and search
$page = (int)($_GET['page'] ?? 1);
$search = sanitizeInput($_GET['search'] ?? '');
$roleFilter = (int)($_GET['role'] ?? 0);

if ($search) {
    $usersData = $userModel->searchUsers($search, $page);
} else {
    $usersData = $userModel->getAllUsers($page, ITEMS_PER_PAGE, $roleFilter);
}

// Get roles for dropdown
$roles = $userModel->getAllRoles();

$pageTitle = __('users_management', 'Quản lý người dùng') . ' - ' . APP_NAME;
?>
<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?></title>

    <!-- MDBootstrap CSS -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/mdb-ui-kit/6.4.2/mdb.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="../../assets/css/custom.css" rel="stylesheet">
</head>
<body>
    <!-- Sidebar -->
    <nav class="sidebar">
        <div class="p-3">
            <div class="text-center mb-4">
                <i class="fas fa-utensils fa-2x text-white mb-2"></i>
                <h5 class="text-white mb-0"><?php echo APP_NAME; ?></h5>
                <small class="text-white-50"><?php echo __('admin', 'Quản trị viên'); ?></small>
            </div>

            <div class="mb-3">
                <small class="text-white-50 text-uppercase"><?php echo __('dashboard_welcome', 'Chào mừng'); ?></small>
                <div class="text-white">
                    <i class="fas fa-user-shield me-2"></i>
                    <?php echo htmlspecialchars($_SESSION['full_name']); ?>
                </div>
            </div>

            <hr class="text-white-50">

            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link" href="dashboard.php">
                        <i class="fas fa-tachometer-alt me-2"></i><?php echo __('dashboard', 'Bảng điều khiển'); ?>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="categories.php">
                        <i class="fas fa-tags me-2"></i><?php echo __('categories', 'Danh mục'); ?>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="food-items.php">
                        <i class="fas fa-hamburger me-2"></i><?php echo __('food_items', 'Món ăn'); ?>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="tables.php">
                        <i class="fas fa-chair me-2"></i><?php echo __('tables', 'Bàn'); ?>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link active" href="users.php">
                        <i class="fas fa-users me-2"></i><?php echo __('users', 'Người dùng'); ?>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="orders.php">
                        <i class="fas fa-receipt me-2"></i><?php echo __('orders', 'Đơn hàng'); ?>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="reports.php">
                        <i class="fas fa-chart-bar me-2"></i><?php echo __('reports', 'Báo cáo'); ?>
                    </a>
                </li>
            </ul>

            <hr class="text-white-50">

            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link" href="../auth/logout.php">
                        <i class="fas fa-sign-out-alt me-2"></i><?php echo __('logout', 'Đăng xuất'); ?>
                    </a>
                </li>
            </ul>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="main-content">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2><i class="fas fa-users me-2"></i><?php echo __('users_management', 'Quản lý người dùng'); ?></h2>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#userModal">
                <i class="fas fa-plus me-2"></i><?php echo __('add_user', 'Thêm người dùng'); ?>
            </button>
        </div>

        <?php displayFlashMessage(); ?>

        <!-- Search and Filter -->
        <div class="card mb-4">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-5">
                        <div class="form-outline">
                            <input type="text" id="search" name="search" class="form-control"
                                   value="<?php echo htmlspecialchars($search); ?>">
                            <label class="form-label" for="search"><?php echo __('search_users', 'Tìm kiếm người dùng...'); ?></label>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <select id="role" name="role" class="form-select">
                            <option value=""><?php echo __('all_roles', 'Tất cả vai trò'); ?></option>
                            <?php foreach ($roles as $role): ?>
                                <option value="<?php echo $role['role_id']; ?>"
                                        <?php echo $roleFilter == $role['role_id'] ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($role['role_name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <button type="submit" class="btn btn-outline-primary me-2">
                            <i class="fas fa-search me-1"></i><?php echo __('search', 'Tìm kiếm'); ?>
                        </button>
                        <a href="users.php" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-1"></i><?php echo __('clear', 'Xóa'); ?>
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- Users Table -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><?php echo __('users_list', 'Danh sách người dùng'); ?></h5>
                <span class="badge bg-primary">
                    <?php echo __('total', 'Tổng cộng'); ?>: <?php echo $usersData['total'] ?? 0; ?>
                </span>
            </div>
            <div class="card-body">
                <?php if (empty($usersData['users'])): ?>
                    <div class="text-center py-4">
                        <i class="fas fa-users fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted"><?php echo __('no_users_found', 'Không tìm thấy người dùng'); ?></h5>
                        <p class="text-muted">
                            <?php echo ($search || $roleFilter) ? __('adjust_search_criteria', 'Thử điều chỉnh tiêu chí tìm kiếm.') : __('add_first_user', 'Bắt đầu bằng cách thêm người dùng đầu tiên.'); ?>
                        </p>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th><?php echo __('username', 'Tên đăng nhập'); ?></th>
                                    <th><?php echo __('full_name', 'Họ và tên'); ?></th>
                                    <th><?php echo __('email', 'Email'); ?></th>
                                    <th><?php echo __('role', 'Vai trò'); ?></th>
                                    <th><?php echo __('status', 'Trạng thái'); ?></th>
                                    <th><?php echo __('created_at', 'Ngày tạo'); ?></th>
                                    <th><?php echo __('actions', 'Thao tác'); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($usersData['users'] as $user): ?>
                                    <tr>
                                        <td><?php echo $user['user_id']; ?></td>
                                        <td>
                                            <strong><?php echo htmlspecialchars($user['username']); ?></strong>
                                            <?php if ($user['user_id'] === $_SESSION['user_id']): ?>
                                                <span class="badge bg-info ms-1"><?php echo __('you', 'Bạn'); ?></span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo htmlspecialchars($user['full_name']); ?></td>
                                        <td><?php echo htmlspecialchars($user['email']); ?></td>
                                        <td>
                                            <span class="badge bg-<?php echo $user['role_name'] === 'admin' ? 'danger' : 'primary'; ?>">
                                                <?php echo $user['role_name'] === 'admin' ? __('admin', 'Quản trị viên') : __('staff', 'Nhân viên'); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge bg-<?php echo $user['status'] ? 'success' : 'danger'; ?>">
                                                <?php echo $user['status'] ? __('active', 'Hoạt động') : __('inactive', 'Không hoạt động'); ?>
                                            </span>
                                        </td>
                                        <td><?php echo formatDate($user['created_at']); ?></td>
                                        <td>
                                            <a href="?edit=<?php echo $user['user_id']; ?>"
                                               class="btn btn-sm btn-outline-primary btn-action me-1"
                                               data-bs-toggle="tooltip" title="<?php echo __('edit', 'Sửa'); ?>">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <?php if ($user['user_id'] !== $_SESSION['user_id']): ?>
                                                <button type="button"
                                                        class="btn btn-sm btn-outline-danger btn-action"
                                                        data-bs-toggle="tooltip" title="<?php echo __('delete', 'Xóa'); ?>"
                                                        onclick="confirmDelete(<?php echo $user['user_id']; ?>, '<?php echo htmlspecialchars($user['username']); ?>')">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <?php if ($usersData['pages'] > 1): ?>
                        <div class="mt-3">
                            <?php
                            $params = [];
                            if ($search) $params['search'] = $search;
                            if ($roleFilter) $params['role'] = $roleFilter;
                            echo generatePagination($usersData['current_page'], $usersData['pages'], 'users.php', $params);
                            ?>
                        </div>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- User Modal -->
    <div class="modal fade" id="userModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-user me-2"></i>
                        <?php echo $editUser ? __('edit_user', 'Sửa người dùng') : __('add_new_user', 'Thêm người dùng mới'); ?>
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                        <input type="hidden" name="action" value="<?php echo $editUser ? 'update' : 'create'; ?>">
                        <?php if ($editUser): ?>
                            <input type="hidden" name="user_id" value="<?php echo $editUser['user_id']; ?>">
                        <?php endif; ?>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-outline mb-3">
                                    <input type="text" id="username" name="username" class="form-control"
                                           value="<?php echo $editUser ? htmlspecialchars($editUser['username']) : ''; ?>" required>
                                    <label class="form-label" for="username"><?php echo __('username', 'Tên đăng nhập'); ?> *</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-outline mb-3">
                                    <input type="email" id="email" name="email" class="form-control"
                                           value="<?php echo $editUser ? htmlspecialchars($editUser['email']) : ''; ?>" required>
                                    <label class="form-label" for="email"><?php echo __('email', 'Email'); ?> *</label>
                                </div>
                            </div>
                        </div>

                        <div class="form-outline mb-3">
                            <input type="text" id="full_name" name="full_name" class="form-control"
                                   value="<?php echo $editUser ? htmlspecialchars($editUser['full_name']) : ''; ?>" required>
                            <label class="form-label" for="full_name"><?php echo __('full_name', 'Họ và tên'); ?> *</label>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-outline mb-3">
                                    <input type="tel" id="phone" name="phone" class="form-control"
                                           value="<?php echo $editUser ? htmlspecialchars($editUser['phone']) : ''; ?>">
                                    <label class="form-label" for="phone"><?php echo __('phone', 'Số điện thoại'); ?></label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="role_id" class="form-label"><?php echo __('role', 'Vai trò'); ?> *</label>
                                    <select id="role_id" name="role_id" class="form-select" required>
                                        <option value=""><?php echo __('select_role', 'Chọn vai trò'); ?></option>
                                        <?php foreach ($roles as $role): ?>
                                            <option value="<?php echo $role['role_id']; ?>"
                                                    <?php echo ($editUser && $editUser['role_id'] == $role['role_id']) ? 'selected' : ''; ?>>
                                                <?php echo $role['role_name'] === 'admin' ? __('admin', 'Quản trị viên') : __('staff', 'Nhân viên'); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-outline mb-3">
                                    <input type="password" id="password" name="password" class="form-control"
                                           <?php echo !$editUser ? 'required' : ''; ?>>
                                    <label class="form-label" for="password">
                                        <?php echo __('password', 'Mật khẩu'); ?><?php echo !$editUser ? ' *' : ' (' . __('leave_blank_keep_current', 'để trống để giữ nguyên') . ')'; ?>
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-outline mb-3">
                                    <input type="password" id="confirm_password" name="confirm_password" class="form-control"
                                           <?php echo !$editUser ? 'required' : ''; ?>>
                                    <label class="form-label" for="confirm_password"><?php echo __('confirm_password', 'Xác nhận mật khẩu'); ?><?php echo !$editUser ? ' *' : ''; ?></label>
                                </div>
                            </div>
                        </div>

                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="status" name="status" value="1"
                                   <?php echo (!$editUser || $editUser['status']) ? 'checked' : ''; ?>>
                            <label class="form-check-label" for="status"><?php echo __('active', 'Hoạt động'); ?></label>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?php echo __('cancel', 'Hủy'); ?></button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            <?php echo $editUser ? __('update', 'Cập nhật') : __('create', 'Tạo mới'); ?>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-exclamation-triangle me-2 text-danger"></i>
                        <?php echo __('confirm_delete', 'Xác nhận xóa'); ?>
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p><?php echo __('confirm_delete_user', 'Bạn có chắc chắn muốn xóa người dùng'); ?> "<span id="deleteUserName"></span>"?</p>
                    <p class="text-danger small">
                        <i class="fas fa-warning me-1"></i>
                        <?php echo __('action_cannot_undone_user', 'Hành động này không thể hoàn tác. Tất cả dữ liệu liên quan đến người dùng này sẽ bị xóa.'); ?>
                    </p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?php echo __('cancel', 'Hủy'); ?></button>
                    <form method="POST" style="display: inline;">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                        <input type="hidden" name="action" value="delete">
                        <input type="hidden" name="user_id" id="deleteUserId">
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-trash me-2"></i><?php echo __('delete', 'Xóa'); ?>
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- MDBootstrap JS -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/mdb-ui-kit/6.4.2/mdb.min.js"></script>
    <!-- Custom JS -->
    <script src="../../assets/js/app.js"></script>

    <script>
        // Show modal if editing
        <?php if ($editUser): ?>
        document.addEventListener('DOMContentLoaded', function() {
            const modal = new mdb.Modal(document.getElementById('userModal'));
            modal.show();
        });
        <?php endif; ?>

        // Delete confirmation
        function confirmDelete(userId, userName) {
            document.getElementById('deleteUserId').value = userId;
            document.getElementById('deleteUserName').textContent = userName;
            const modal = new mdb.Modal(document.getElementById('deleteModal'));
            modal.show();
        }

        // Password confirmation validation
        document.addEventListener('DOMContentLoaded', function() {
            const password = document.getElementById('password');
            const confirmPassword = document.getElementById('confirm_password');

            function validatePassword() {
                if (password.value !== confirmPassword.value) {
                    confirmPassword.setCustomValidity('<?php echo __('password_mismatch', 'Mật khẩu xác nhận không khớp.'); ?>');
                } else {
                    confirmPassword.setCustomValidity('');
                }
            }

            password.addEventListener('change', validatePassword);
            confirmPassword.addEventListener('keyup', validatePassword);

            // Initialize tooltips
            const tooltips = document.querySelectorAll('[data-bs-toggle="tooltip"]');
            tooltips.forEach(tooltip => {
                new mdb.Tooltip(tooltip);
            });
        });
    </script>
</body>
</html>