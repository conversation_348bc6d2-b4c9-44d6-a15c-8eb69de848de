<?php
/**
 * Database Setup Script
 * 
 * This script sets up the database and ensures proper password hashes
 */

require_once 'config/config.php';
require_once 'config/database.php';

echo "<h2>Restaurant Management System - Database Setup</h2>";

if ($_POST['action'] ?? '' === 'setup') {
    echo "<h3>Setting up database...</h3>";
    
    try {
        // Check if database exists
        $stmt = $pdo->query("SELECT DATABASE()");
        $currentDb = $stmt->fetchColumn();
        echo "✅ Connected to database: $currentDb<br>";
        
        // Check if users table exists
        $stmt = $pdo->query("SHOW TABLES LIKE 'users'");
        if ($stmt->rowCount() > 0) {
            echo "✅ Users table exists<br>";
            
            // Update password hashes
            $adminHash = password_hash('admin123', PASSWORD_DEFAULT);
            $staffHash = password_hash('staff123', PASSWORD_DEFAULT);
            
            // Update admin password
            $stmt = $pdo->prepare("UPDATE users SET password = ? WHERE username = 'admin'");
            if ($stmt->execute([$adminHash])) {
                echo "✅ Admin password hash updated<br>";
            } else {
                echo "❌ Failed to update admin password<br>";
            }
            
            // Update staff password
            $stmt = $pdo->prepare("UPDATE users SET password = ? WHERE username = 'staff'");
            if ($stmt->execute([$staffHash])) {
                echo "✅ Staff password hash updated<br>";
            } else {
                echo "❌ Failed to update staff password<br>";
            }
            
            // Verify the updates
            echo "<h4>Verification:</h4>";
            $stmt = $pdo->query("SELECT username, password FROM users WHERE username IN ('admin', 'staff')");
            $users = $stmt->fetchAll();
            
            foreach ($users as $user) {
                $testPass = $user['username'] === 'admin' ? 'admin123' : 'staff123';
                $isValid = password_verify($testPass, $user['password']);
                echo "User '{$user['username']}' with password '$testPass': " . 
                     ($isValid ? "✅ VALID" : "❌ INVALID") . "<br>";
            }
            
            echo "<p style='color: green; font-weight: bold; margin-top: 20px;'>✅ Database setup completed successfully!</p>";
            echo "<p><a href='views/auth/login.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Go to Login Page</a></p>";
            
        } else {
            echo "❌ Users table does not exist. Please import the database.sql file first.<br>";
            echo "<p>To import the database:</p>";
            echo "<ol>";
            echo "<li>Open your MySQL client (phpMyAdmin, MySQL Workbench, or command line)</li>";
            echo "<li>Create the database: <code>CREATE DATABASE restaurant_management;</code></li>";
            echo "<li>Import the SQL file: <code>mysql -u root -p restaurant_management < database.sql</code></li>";
            echo "<li>Then run this setup script again</li>";
            echo "</ol>";
        }
        
    } catch (PDOException $e) {
        echo "❌ Database error: " . $e->getMessage() . "<br>";
        echo "<p>Please check your database configuration in config/database.php</p>";
    }
    
} else {
    // Show setup form
    echo "<p>This script will set up the database with proper password hashes for the default users.</p>";
    echo "<p><strong>Default Login Credentials:</strong></p>";
    echo "<ul>";
    echo "<li><strong>Admin:</strong> username = admin, password = admin123</li>";
    echo "<li><strong>Staff:</strong> username = staff, password = staff123</li>";
    echo "</ul>";
    
    echo "<p><strong>Before running this setup:</strong></p>";
    echo "<ol>";
    echo "<li>Make sure you have imported the database.sql file</li>";
    echo "<li>Verify your database connection settings in config/database.php</li>";
    echo "<li>Ensure your web server has write permissions for error logging</li>";
    echo "</ol>";
    
    echo "<form method='POST'>";
    echo "<input type='hidden' name='action' value='setup'>";
    echo "<button type='submit' style='background: #007bff; color: white; padding: 15px 30px; border: none; border-radius: 5px; cursor: pointer; font-size: 16px;'>Setup Database</button>";
    echo "</form>";
    
    echo "<hr>";
    echo "<p><strong>Troubleshooting Links:</strong></p>";
    echo "<ul>";
    echo "<li><a href='debug_auth.php'>Debug Authentication</a></li>";
    echo "<li><a href='generate_hashes.php'>Generate Password Hashes</a></li>";
    echo "<li><a href='views/auth/login.php'>Login Page</a></li>";
    echo "</ul>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
h2 { color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px; }
h3 { color: #007bff; margin-top: 30px; }
h4 { color: #28a745; margin-top: 20px; }
code { background: #f8f9fa; padding: 2px 4px; border-radius: 3px; font-family: monospace; }
ul, ol { margin: 10px 0; padding-left: 30px; }
li { margin: 5px 0; }
button { transition: background-color 0.3s; }
button:hover { background: #0056b3 !important; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
</style>
