<?php
/**
 * Vietnamese Localization Script
 * 
 * This script helps complete the Vietnamese localization of the Restaurant Management System
 */

require_once 'config/config.php';
require_once 'config/database.php';

echo "<h2>Hệ Thống Quản Lý Nhà Hàng - Việt Hóa</h2>";

if ($_POST['action'] ?? '' === 'localize') {
    echo "<h3><PERSON><PERSON> thực hiện việt hóa...</h3>";
    
    try {
        // Update user data to Vietnamese
        echo "<h4>1. Cập nhật dữ liệu người dùng</h4>";
        
        $stmt = $pdo->prepare("UPDATE users SET full_name = ? WHERE username = 'admin'");
        $stmt->execute(['Quản trị viên hệ thống']);
        echo "✅ Cập nhật tên quản trị viên<br>";
        
        $stmt = $pdo->prepare("UPDATE users SET full_name = ? WHERE username = 'staff'");
        $stmt->execute(['Nhân viên phục vụ']);
        echo "✅ Cập nhật tên nhân viên<br>";
        
        // Update role names to Vietnamese
        echo "<h4>2. Cập nhật tên vai trò</h4>";
        
        $stmt = $pdo->prepare("UPDATE roles SET role_name = ? WHERE role_id = 1");
        $stmt->execute(['admin']);
        echo "✅ Cập nhật vai trò quản trị viên<br>";
        
        $stmt = $pdo->prepare("UPDATE roles SET role_name = ? WHERE role_id = 2");
        $stmt->execute(['staff']);
        echo "✅ Cập nhật vai trò nhân viên<br>";
        
        // Update categories to Vietnamese if they exist
        echo "<h4>3. Cập nhật danh mục món ăn</h4>";
        
        $categories = [
            'Appetizers' => 'Khai vị',
            'Main Course' => 'Món chính', 
            'Desserts' => 'Tráng miệng',
            'Beverages' => 'Đồ uống',
            'Specials' => 'Món đặc biệt'
        ];
        
        foreach ($categories as $english => $vietnamese) {
            $stmt = $pdo->prepare("UPDATE categories SET category_name = ? WHERE category_name = ?");
            if ($stmt->execute([$vietnamese, $english])) {
                echo "✅ Cập nhật danh mục: $english → $vietnamese<br>";
            }
        }
        
        // Update food items to Vietnamese if they exist
        echo "<h4>4. Cập nhật món ăn</h4>";
        
        $foodItems = [
            'Spring Rolls' => 'Chả giò',
            'Chicken Wings' => 'Cánh gà',
            'Grilled Salmon' => 'Cá hồi nướng',
            'Beef Steak' => 'Bít tết bò',
            'Chocolate Cake' => 'Bánh chocolate',
            'Cheesecake' => 'Bánh phô mai',
            'Lemonade' => 'Nước chanh',
            'Iced Tea' => 'Trà đá',
            'Chef\'s Special Pasta' => 'Mì Ý đặc biệt'
        ];
        
        foreach ($foodItems as $english => $vietnamese) {
            $stmt = $pdo->prepare("UPDATE food_items SET food_name = ? WHERE food_name = ?");
            if ($stmt->execute([$vietnamese, $english])) {
                echo "✅ Cập nhật món ăn: $english → $vietnamese<br>";
            }
        }
        
        // Update table status to Vietnamese format
        echo "<h4>5. Cập nhật trạng thái bàn</h4>";
        
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM tables");
        $tableCount = $stmt->fetch()['count'];
        echo "✅ Tìm thấy $tableCount bàn trong hệ thống<br>";
        
        // Test currency formatting
        echo "<h4>6. Kiểm tra định dạng tiền tệ</h4>";
        
        $testAmounts = [50000, 125000, 1500000];
        foreach ($testAmounts as $amount) {
            echo "Số tiền $amount → " . formatCurrency($amount) . "<br>";
        }
        echo "✅ Định dạng tiền tệ hoạt động đúng<br>";
        
        // Test date formatting
        echo "<h4>7. Kiểm tra định dạng ngày tháng</h4>";
        
        $testDate = date('Y-m-d H:i:s');
        echo "Ngày hiện tại: " . formatDate($testDate) . "<br>";
        echo "Ngày tiếng Việt: " . formatVietnameseDate($testDate) . "<br>";
        echo "✅ Định dạng ngày tháng hoạt động đúng<br>";
        
        // Test language functions
        echo "<h4>8. Kiểm tra hàm ngôn ngữ</h4>";
        
        $testKeys = ['dashboard', 'categories', 'food_items', 'tables', 'orders'];
        foreach ($testKeys as $key) {
            echo "$key → " . __($key) . "<br>";
        }
        echo "✅ Hàm dịch ngôn ngữ hoạt động đúng<br>";
        
        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h4>🎉 Việt hóa hoàn tất!</h4>";
        echo "<p><strong>Những gì đã được cập nhật:</strong></p>";
        echo "<ul>";
        echo "<li>✅ Tên người dùng và vai trò</li>";
        echo "<li>✅ Danh mục món ăn</li>";
        echo "<li>✅ Tên món ăn</li>";
        echo "<li>✅ Định dạng tiền tệ VND</li>";
        echo "<li>✅ Định dạng ngày tháng Việt Nam</li>";
        echo "<li>✅ Hệ thống dịch ngôn ngữ</li>";
        echo "</ul>";
        echo "<p><strong>Bước tiếp theo:</strong></p>";
        echo "<ul>";
        echo "<li>Đăng nhập vào hệ thống để kiểm tra giao diện tiếng Việt</li>";
        echo "<li>Kiểm tra các trang quản lý danh mục và món ăn</li>";
        echo "<li>Thử tạo đơn hàng mới với định dạng tiền tệ VND</li>";
        echo "</ul>";
        echo "</div>";
        
        echo "<p><a href='views/auth/login.php' style='background: #28a745; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-size: 16px;'>Đăng nhập vào hệ thống</a></p>";
        
    } catch (PDOException $e) {
        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "❌ Lỗi cơ sở dữ liệu: " . $e->getMessage();
        echo "</div>";
    }
    
} else {
    // Show localization form
    echo "<p>Script này sẽ việt hóa toàn bộ hệ thống quản lý nhà hàng, bao gồm:</p>";
    echo "<ul>";
    echo "<li><strong>Giao diện người dùng:</strong> Dịch tất cả văn bản từ tiếng Anh sang tiếng Việt</li>";
    echo "<li><strong>Dữ liệu mẫu:</strong> Cập nhật tên danh mục và món ăn sang tiếng Việt</li>";
    echo "<li><strong>Định dạng tiền tệ:</strong> Sử dụng VND với định dạng Việt Nam</li>";
    echo "<li><strong>Định dạng ngày tháng:</strong> Sử dụng định dạng dd/mm/yyyy</li>";
    echo "<li><strong>Múi giờ:</strong> Đặt múi giờ Asia/Ho_Chi_Minh</li>";
    echo "</ul>";
    
    echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4>⚠️ Lưu ý quan trọng:</h4>";
    echo "<ul>";
    echo "<li>Đảm bảo bạn đã import file database.sql</li>";
    echo "<li>Đảm bảo kết nối cơ sở dữ liệu hoạt động bình thường</li>";
    echo "<li>Script này sẽ thay đổi dữ liệu trong cơ sở dữ liệu</li>";
    echo "<li>Nên sao lưu cơ sở dữ liệu trước khi chạy</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<form method='POST'>";
    echo "<input type='hidden' name='action' value='localize'>";
    echo "<button type='submit' style='background: #007bff; color: white; padding: 15px 30px; border: none; border-radius: 5px; cursor: pointer; font-size: 16px;'>Bắt đầu việt hóa hệ thống</button>";
    echo "</form>";
    
    echo "<hr>";
    echo "<p><strong>Các công cụ khác:</strong></p>";
    echo "<ul>";
    echo "<li><a href='test_db_connection.php'>Kiểm tra kết nối cơ sở dữ liệu</a></li>";
    echo "<li><a href='test_login.php'>Kiểm tra đăng nhập</a></li>";
    echo "<li><a href='setup_database.php'>Thiết lập cơ sở dữ liệu</a></li>";
    echo "<li><a href='views/auth/login.php'>Trang đăng nhập</a></li>";
    echo "</ul>";
}

// Display current localization status
echo "<hr>";
echo "<h3>Trạng thái việt hóa hiện tại</h3>";

try {
    // Check language file
    if (function_exists('__')) {
        echo "✅ File ngôn ngữ tiếng Việt đã được tải<br>";
        echo "Ví dụ: 'dashboard' → " . __('dashboard') . "<br>";
    } else {
        echo "❌ File ngôn ngữ chưa được tải<br>";
    }
    
    // Check currency formatting
    echo "✅ Định dạng tiền tệ: " . formatCurrency(100000) . "<br>";
    
    // Check date formatting
    echo "✅ Định dạng ngày: " . formatDate(date('Y-m-d H:i:s')) . "<br>";
    
    // Check timezone
    echo "✅ Múi giờ: " . date_default_timezone_get() . "<br>";
    
    // Check database encoding
    if ($pdo) {
        $stmt = $pdo->query("SELECT @@character_set_database as charset");
        $charset = $stmt->fetch()['charset'];
        echo "✅ Mã hóa cơ sở dữ liệu: $charset<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Lỗi kiểm tra: " . $e->getMessage() . "<br>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
h2 { color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px; }
h3 { color: #007bff; margin-top: 30px; border-left: 4px solid #007bff; padding-left: 10px; }
h4 { color: #28a745; margin-top: 20px; }
ul { margin: 10px 0; padding-left: 30px; }
li { margin: 5px 0; }
button { transition: background-color 0.3s; }
button:hover { opacity: 0.9; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
</style>
