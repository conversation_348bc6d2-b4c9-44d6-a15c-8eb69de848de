<?php
/**
 * Admin Dashboard
 * 
 * Main dashboard for restaurant administrators with overview statistics,
 * quick actions, and navigation to management sections.
 */

require_once '../../config/config.php';
require_once '../../config/database.php';
require_once '../../includes/functions.php';
require_once '../../models/User.php';
require_once '../../models/Category.php';
require_once '../../models/FoodItem.php';
require_once '../../models/Table.php';
require_once '../../models/Order.php';
require_once '../../models/Payment.php';

// Check admin access
requireAdmin();

// Initialize models
$userModel = new User();
$categoryModel = new Category();
$foodItemModel = new FoodItem();
$tableModel = new Table();
$orderModel = new Order();
$paymentModel = new Payment();

// Get dashboard statistics
$tableStats = $tableModel->getTableStats();
$revenueStats = $paymentModel->getRevenueSummary();
$activeOrders = $orderModel->getActiveOrders();
$recentPayments = $paymentModel->getRecentPayments(5);
$popularItems = $foodItemModel->getPopularFoodItems(5);

$pageTitle = __('dashboard') . ' ' . __('admin') . ' - ' . APP_NAME;
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?></title>
    
    <!-- MDBootstrap CSS -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/mdb-ui-kit/6.4.2/mdb.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        .sidebar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            position: fixed;
            top: 0;
            left: 0;
            width: 250px;
            z-index: 1000;
        }
        .main-content {
            margin-left: 250px;
            padding: 20px;
        }
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1rem;
        }
        .stat-card .stat-icon {
            font-size: 2.5rem;
            opacity: 0.8;
        }
        .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 0.75rem 1rem;
            border-radius: 8px;
            margin: 0.25rem 0;
            transition: all 0.3s ease;
        }
        .nav-link:hover, .nav-link.active {
            background: rgba(255, 255, 255, 0.1);
            color: white;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        .table-status-available { color: #28a745; }
        .table-status-occupied { color: #dc3545; }
        .table-status-reserved { color: #ffc107; }
        .table-status-maintenance { color: #6c757d; }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <nav class="sidebar">
        <div class="p-3">
            <div class="text-center mb-4">
                <i class="fas fa-utensils fa-2x text-white mb-2"></i>
                <h5 class="text-white mb-0"><?php echo APP_NAME; ?></h5>
                <small class="text-white-50"><?php echo __('admin', 'Quản trị viên'); ?></small>
            </div>

            <div class="mb-3">
                <small class="text-white-50 text-uppercase"><?php echo __('dashboard_welcome', 'Chào mừng'); ?></small>
                <div class="text-white">
                    <i class="fas fa-user-shield me-2"></i>
                    <?php echo htmlspecialchars($_SESSION['full_name']); ?>
                </div>
            </div>
            
            <hr class="text-white-50">
            
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link active" href="dashboard.php">
                        <i class="fas fa-tachometer-alt me-2"></i><?php echo __('dashboard', 'Bảng điều khiển'); ?>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="categories.php">
                        <i class="fas fa-tags me-2"></i><?php echo __('categories', 'Danh mục'); ?>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="food-items.php">
                        <i class="fas fa-hamburger me-2"></i><?php echo __('food_items', 'Món ăn'); ?>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="tables.php">
                        <i class="fas fa-chair me-2"></i><?php echo __('tables', 'Bàn'); ?>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="users.php">
                        <i class="fas fa-users me-2"></i><?php echo __('users', 'Người dùng'); ?>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="orders.php">
                        <i class="fas fa-receipt me-2"></i><?php echo __('orders', 'Đơn hàng'); ?>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="reports.php">
                        <i class="fas fa-chart-bar me-2"></i><?php echo __('reports', 'Báo cáo'); ?>
                    </a>
                </li>
            </ul>
            
            <hr class="text-white-50">
            
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link" href="../auth/logout.php">
                        <i class="fas fa-sign-out-alt me-2"></i>Logout
                    </a>
                </li>
            </ul>
        </div>
    </nav>
    
    <!-- Main Content -->
    <div class="main-content">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2><i class="fas fa-tachometer-alt me-2"></i>Admin Dashboard</h2>
            <div class="text-muted">
                <i class="fas fa-calendar me-1"></i>
                <?php echo date('l, F j, Y'); ?>
            </div>
        </div>
        
        <?php displayFlashMessage(); ?>
        
        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="mb-0"><?php echo formatCurrency($revenueStats['today_revenue']); ?></h3>
                            <p class="mb-0">Today's Revenue</p>
                            <small><?php echo $revenueStats['today_orders']; ?> orders</small>
                        </div>
                        <i class="fas fa-dollar-sign stat-icon"></i>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="mb-0"><?php echo $tableStats['available_tables']; ?>/<?php echo $tableStats['total_tables']; ?></h3>
                            <p class="mb-0">Available Tables</p>
                            <small><?php echo $tableStats['occupied_tables']; ?> occupied</small>
                        </div>
                        <i class="fas fa-chair stat-icon"></i>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="mb-0"><?php echo count($activeOrders); ?></h3>
                            <p class="mb-0">Active Orders</p>
                            <small>Pending & Processing</small>
                        </div>
                        <i class="fas fa-receipt stat-icon"></i>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="mb-0"><?php echo formatCurrency($revenueStats['month_revenue']); ?></h3>
                            <p class="mb-0">Monthly Revenue</p>
                            <small><?php echo $revenueStats['month_orders']; ?> orders</small>
                        </div>
                        <i class="fas fa-chart-line stat-icon"></i>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row">
            <!-- Active Orders -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="fas fa-receipt me-2"></i>Active Orders</h5>
                        <a href="orders.php" class="btn btn-sm btn-outline-primary">View All</a>
                    </div>
                    <div class="card-body">
                        <?php if (empty($activeOrders)): ?>
                            <div class="text-center text-muted py-3">
                                <i class="fas fa-inbox fa-2x mb-2"></i>
                                <p>No active orders</p>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>Order #</th>
                                            <th>Table</th>
                                            <th>Amount</th>
                                            <th>Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($activeOrders as $order): ?>
                                            <tr>
                                                <td>#<?php echo $order['order_id']; ?></td>
                                                <td><?php echo htmlspecialchars($order['table_number']); ?></td>
                                                <td><?php echo formatCurrency($order['final_amount']); ?></td>
                                                <td>
                                                    <span class="badge bg-<?php echo $order['status'] === 'pending' ? 'warning' : 'info'; ?>">
                                                        <?php echo ucfirst($order['status']); ?>
                                                    </span>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <!-- Recent Payments -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="fas fa-credit-card me-2"></i>Recent Payments</h5>
                        <a href="reports.php" class="btn btn-sm btn-outline-primary">View Reports</a>
                    </div>
                    <div class="card-body">
                        <?php if (empty($recentPayments)): ?>
                            <div class="text-center text-muted py-3">
                                <i class="fas fa-credit-card fa-2x mb-2"></i>
                                <p>No recent payments</p>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>Table</th>
                                            <th>Amount</th>
                                            <th>Method</th>
                                            <th>Time</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($recentPayments as $payment): ?>
                                            <tr>
                                                <td><?php echo htmlspecialchars($payment['table_number']); ?></td>
                                                <td><?php echo formatCurrency($payment['payment_amount']); ?></td>
                                                <td>
                                                    <span class="badge bg-secondary">
                                                        <?php echo ucfirst(str_replace('_', ' ', $payment['payment_method'])); ?>
                                                    </span>
                                                </td>
                                                <td><?php echo formatDate($payment['payment_date'], 'H:i'); ?></td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <!-- Popular Items -->
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-star me-2"></i>Popular Food Items</h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($popularItems)): ?>
                            <div class="text-center text-muted py-3">
                                <i class="fas fa-hamburger fa-2x mb-2"></i>
                                <p>No sales data available</p>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>Food Item</th>
                                            <th>Category</th>
                                            <th>Total Ordered</th>
                                            <th>Revenue</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($popularItems as $item): ?>
                                            <tr>
                                                <td>
                                                    <strong><?php echo htmlspecialchars($item['food_name']); ?></strong>
                                                </td>
                                                <td><?php echo htmlspecialchars($item['category_name']); ?></td>
                                                <td>
                                                    <span class="badge bg-primary"><?php echo $item['total_ordered']; ?></span>
                                                </td>
                                                <td><?php echo formatCurrency($item['total_revenue']); ?></td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- MDBootstrap JS -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/mdb-ui-kit/6.4.2/mdb.min.js"></script>
    
    <script>
        // Auto-refresh dashboard every 30 seconds
        setInterval(function() {
            // Only refresh if user is still on the page
            if (document.visibilityState === 'visible') {
                location.reload();
            }
        }, 30000);
        
        // Initialize tooltips
        document.addEventListener('DOMContentLoaded', function() {
            const tooltips = document.querySelectorAll('[data-bs-toggle="tooltip"]');
            tooltips.forEach(tooltip => {
                new mdb.Tooltip(tooltip);
            });
        });
    </script>
</body>
</html>
