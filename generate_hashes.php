<?php
/**
 * Password Hash Generator
 * 
 * This script generates proper password hashes for the default users
 */

echo "<h2>Password Hash Generator</h2>";

// Generate hashes for default users
$adminPassword = 'admin123';
$staffPassword = 'staff123';

$adminHash = password_hash($adminPassword, PASSWORD_DEFAULT);
$staffHash = password_hash($staffPassword, PASSWORD_DEFAULT);

echo "<h3>Generated Password Hashes:</h3>";
echo "<p><strong>Admin (password: admin123):</strong><br>";
echo "<code>$adminHash</code></p>";

echo "<p><strong>Staff (password: staff123):</strong><br>";
echo "<code>$staffHash</code></p>";

echo "<h3>SQL Update Statements:</h3>";
echo "<p>Copy and run these SQL statements to update the password hashes:</p>";
echo "<pre>";
echo "UPDATE users SET password = '$adminHash' WHERE username = 'admin';\n";
echo "UPDATE users SET password = '$staffHash' WHERE username = 'staff';\n";
echo "</pre>";

echo "<h3>Verification Test:</h3>";
echo "<p>Admin password verification: " . (password_verify($adminPassword, $adminHash) ? "✅ PASS" : "❌ FAIL") . "</p>";
echo "<p>Staff password verification: " . (password_verify($staffPassword, $staffHash) ? "✅ PASS" : "❌ FAIL") . "</p>";

echo "<hr>";
echo "<p><a href='debug_auth.php'>Run Authentication Debug</a> | <a href='views/auth/login.php'>Try Login</a></p>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h2 { color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px; }
h3 { color: #007bff; margin-top: 30px; }
code { background: #f8f9fa; padding: 2px 4px; border-radius: 3px; font-family: monospace; }
pre { background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; border-left: 4px solid #007bff; }
</style>
