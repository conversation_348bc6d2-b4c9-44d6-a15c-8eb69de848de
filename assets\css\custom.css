/**
 * Custom CSS for Restaurant Management System
 * 
 * This file contains custom styles that extend MDBootstrap
 * and provide consistent theming throughout the application.
 */

/* Root Variables */
:root {
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --border-radius: 15px;
    --box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

/* Global Styles */
body {
    font-family: 'Roboto', sans-serif;
    background-color: #f8f9fa;
}

/* Custom Gradients */
.bg-gradient-primary {
    background: var(--primary-gradient);
}

.text-gradient-primary {
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Card Enhancements */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.card-header {
    background: transparent;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    font-weight: 500;
}

/* Button Enhancements */
.btn {
    border-radius: 25px;
    font-weight: 500;
    transition: var(--transition);
}

.btn-primary {
    background: var(--primary-gradient);
    border: none;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.btn-action {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
    border-radius: 8px;
}

/* Sidebar Styles */
.sidebar {
    background: var(--primary-gradient);
    min-height: 100vh;
    position: fixed;
    top: 0;
    left: 0;
    width: 250px;
    z-index: 1000;
    transition: var(--transition);
}

.sidebar .nav-link {
    color: rgba(255, 255, 255, 0.8);
    padding: 0.75rem 1rem;
    border-radius: 8px;
    margin: 0.25rem 0;
    transition: var(--transition);
}

.sidebar .nav-link:hover,
.sidebar .nav-link.active {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    transform: translateX(5px);
}

.main-content {
    margin-left: 250px;
    padding: 20px;
    transition: var(--transition);
}

/* Table Styles */
.table {
    background: white;
    border-radius: var(--border-radius);
    overflow: hidden;
}

.table th {
    background: #f8f9fa;
    border: none;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.875rem;
    letter-spacing: 0.5px;
}

.table td {
    border: none;
    border-bottom: 1px solid #f0f0f0;
    vertical-align: middle;
}

.table-hover tbody tr:hover {
    background-color: rgba(102, 126, 234, 0.05);
}

/* Status Badges */
.badge {
    font-weight: 500;
    padding: 0.5em 0.75em;
}

.table-status-available { color: var(--success-color); }
.table-status-occupied { color: var(--danger-color); }
.table-status-reserved { color: var(--warning-color); }
.table-status-maintenance { color: #6c757d; }

/* Statistics Cards */
.stat-card {
    background: var(--primary-gradient);
    color: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin-bottom: 1rem;
    transition: var(--transition);
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
}

.stat-card .stat-icon {
    font-size: 2.5rem;
    opacity: 0.8;
}

/* Table Cards for Staff Dashboard */
.table-card {
    border-radius: var(--border-radius);
    border: none;
    box-shadow: var(--box-shadow);
    margin-bottom: 1rem;
    transition: var(--transition);
    cursor: pointer;
}

.table-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.table-available {
    border-left: 5px solid var(--success-color);
}

.table-occupied {
    border-left: 5px solid var(--danger-color);
}

.table-reserved {
    border-left: 5px solid var(--warning-color);
}

.table-maintenance {
    border-left: 5px solid #6c757d;
}

/* Form Enhancements */
.form-outline {
    margin-bottom: 1.5rem;
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* Modal Enhancements */
.modal-content {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
}

.modal-header {
    background: var(--primary-gradient);
    color: white;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
}

.modal-header .btn-close {
    filter: invert(1);
}

/* Alert Enhancements */
.alert {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

.alert-success {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
}

.alert-danger {
    background: linear-gradient(135deg, #dc3545, #e83e8c);
    color: white;
}

.alert-warning {
    background: linear-gradient(135deg, #ffc107, #fd7e14);
    color: #212529;
}

.alert-info {
    background: linear-gradient(135deg, #17a2b8, #6f42c1);
    color: white;
}

/* Navigation Enhancements */
.navbar {
    background: var(--primary-gradient);
    box-shadow: var(--box-shadow);
}

.navbar-brand {
    font-weight: 600;
    font-size: 1.5rem;
}

/* Quick Action Buttons */
.quick-action-btn {
    border-radius: 25px;
    padding: 12px 25px;
    font-weight: 500;
    margin: 0.25rem;
    transition: var(--transition);
}

.quick-action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* Loading Spinner */
.spinner-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
    }
    
    .sidebar.show {
        transform: translateX(0);
    }
    
    .main-content {
        margin-left: 0;
        padding: 15px;
    }
    
    .stat-card {
        margin-bottom: 1rem;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
    
    .btn-action {
        padding: 0.2rem 0.4rem;
        font-size: 0.75rem;
    }
}

@media (max-width: 576px) {
    .main-content {
        padding: 10px;
    }
    
    .card {
        margin-bottom: 1rem;
    }
    
    .stat-card {
        padding: 1rem;
    }
    
    .stat-card h3 {
        font-size: 1.5rem;
    }
    
    .table-card {
        margin-bottom: 0.75rem;
    }
}

/* Print Styles */
@media print {
    .sidebar,
    .navbar,
    .btn,
    .modal {
        display: none !important;
    }
    
    .main-content {
        margin-left: 0;
        padding: 0;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #ddd;
    }
    
    .table {
        font-size: 0.875rem;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in-left {
    animation: slideInLeft 0.5s ease-out;
}

@keyframes slideInLeft {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

/* Utility Classes */
.text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.border-gradient {
    border: 2px solid transparent;
    background: linear-gradient(white, white) padding-box,
                var(--primary-gradient) border-box;
}

.glass-effect {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: var(--primary-gradient);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #5a6fd8;
}
