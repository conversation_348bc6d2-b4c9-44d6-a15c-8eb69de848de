<?php
/**
 * New Order Creation Page
 * 
 * Staff interface for creating new orders, selecting tables,
 * adding food items, and calculating totals.
 */

require_once '../../config/config.php';
require_once '../../config/database.php';
require_once '../../includes/functions.php';
require_once '../../models/Order.php';
require_once '../../models/Table.php';
require_once '../../models/FoodItem.php';
require_once '../../models/Category.php';

// Check staff access
requireLogin();

// Initialize models
$orderModel = new Order();
$tableModel = new Table();
$foodItemModel = new FoodItem();
$categoryModel = new Category();

// Get table ID from URL
$tableId = (int)($_GET['table_id'] ?? 0);
$selectedTable = null;

if ($tableId > 0) {
    $selectedTable = $tableModel->getTableById($tableId);
    if (!$selectedTable || $selectedTable['status'] !== 'available') {
        setFlashMessage('error', __('table_not_available', 'Bàn không khả dụng hoặc đã có khách.'));
        header('Location: dashboard.php');
        exit();
    }
}

// Handle order creation
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
        setFlashMessage('error', __('invalid_csrf_token', 'Token bảo mật không hợp lệ. Vui lòng thử lại.'));
    } else {
        $action = $_POST['action'] ?? '';
        
        if ($action === 'create_order') {
            $orderData = [
                'table_id' => (int)($_POST['table_id'] ?? 0),
                'staff_id' => $_SESSION['user_id'],
                'customer_name' => sanitizeInput($_POST['customer_name'] ?? ''),
                'customer_phone' => sanitizeInput($_POST['customer_phone'] ?? ''),
                'notes' => sanitizeInput($_POST['notes'] ?? ''),
                'items' => json_decode($_POST['order_items'] ?? '[]', true)
            ];
            
            // Validation
            if ($orderData['table_id'] <= 0) {
                setFlashMessage('error', __('table_required', 'Vui lòng chọn bàn.'));
            } elseif (empty($orderData['items'])) {
                setFlashMessage('error', __('order_items_required', 'Vui lòng thêm ít nhất một món ăn.'));
            } else {
                $orderId = $orderModel->createOrderWithItems($orderData);
                if ($orderId) {
                    setFlashMessage('success', __('order_created_success', 'Tạo đơn hàng thành công.'));
                    logActivity("Tạo đơn hàng #$orderId cho bàn {$orderData['table_id']}", $_SESSION['user_id']);
                    header("Location: order-details.php?id=$orderId");
                    exit();
                } else {
                    setFlashMessage('error', __('order_create_failed', 'Tạo đơn hàng thất bại. Vui lòng thử lại.'));
                }
            }
        }
    }
}

// Get available tables
$availableTables = $tableModel->getAvailableTables();

// Get food items by category
$categories = $categoryModel->getActiveCategories();
$foodItemsByCategory = [];
foreach ($categories as $category) {
    $foodItemsByCategory[$category['category_id']] = $foodItemModel->getFoodItemsByCategory($category['category_id'], true);
}

$pageTitle = __('new_order', 'Đơn hàng mới') . ' - ' . APP_NAME;
?>
<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?></title>
    
    <!-- MDBootstrap CSS -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/mdb-ui-kit/6.4.2/mdb.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="../../assets/css/custom.css" rel="stylesheet">
    
    <style>
        .food-item-card {
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }
        .food-item-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border-color: #007bff;
        }
        .food-item-image {
            width: 100%;
            height: 120px;
            object-fit: cover;
            border-radius: 8px;
        }
        .order-summary {
            position: sticky;
            top: 20px;
        }
        .order-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            border-bottom: 1px solid #eee;
        }
        .quantity-controls {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .quantity-btn {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .category-tab {
            cursor: pointer;
            padding: 10px 20px;
            border: 1px solid #ddd;
            background: #f8f9fa;
            margin-right: 5px;
            border-radius: 5px;
            transition: all 0.3s ease;
        }
        .category-tab.active {
            background: #007bff;
            color: white;
            border-color: #007bff;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="dashboard.php">
                <i class="fas fa-utensils me-2"></i><?php echo APP_NAME; ?>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-1"></i><?php echo __('dashboard', 'Bảng điều khiển'); ?>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="tables.php">
                            <i class="fas fa-chair me-1"></i><?php echo __('tables', 'Bàn'); ?>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="orders.php">
                            <i class="fas fa-receipt me-1"></i><?php echo __('orders', 'Đơn hàng'); ?>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="new-order.php">
                            <i class="fas fa-plus me-1"></i><?php echo __('new_order', 'Đơn hàng mới'); ?>
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle me-1"></i>
                            <?php echo htmlspecialchars($_SESSION['full_name']); ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="profile.php">
                                <i class="fas fa-user me-2"></i><?php echo __('profile', 'Hồ sơ'); ?>
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../auth/logout.php">
                                <i class="fas fa-sign-out-alt me-2"></i><?php echo __('logout', 'Đăng xuất'); ?>
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    
    <!-- Main Content -->
    <div class="container-fluid mt-4">
        <div class="row">
            <!-- Food Items Section -->
            <div class="col-lg-8">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fas fa-plus me-2"></i><?php echo __('new_order', 'Đơn hàng mới'); ?></h2>
                    <a href="dashboard.php" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i><?php echo __('back_to_dashboard', 'Quay về bảng điều khiển'); ?>
                    </a>
                </div>
                
                <?php displayFlashMessage(); ?>
                
                <!-- Category Tabs -->
                <div class="mb-4">
                    <div class="d-flex flex-wrap">
                        <div class="category-tab active" data-category="all">
                            <i class="fas fa-th-large me-2"></i><?php echo __('all_categories', 'Tất cả danh mục'); ?>
                        </div>
                        <?php foreach ($categories as $category): ?>
                            <div class="category-tab" data-category="<?php echo $category['category_id']; ?>">
                                <i class="fas fa-tag me-2"></i><?php echo htmlspecialchars($category['category_name']); ?>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                
                <!-- Food Items Grid -->
                <div id="foodItemsContainer">
                    <?php foreach ($categories as $category): ?>
                        <div class="category-section" data-category="<?php echo $category['category_id']; ?>">
                            <h4 class="mb-3">
                                <i class="fas fa-tag me-2"></i><?php echo htmlspecialchars($category['category_name']); ?>
                            </h4>
                            <div class="row">
                                <?php if (!empty($foodItemsByCategory[$category['category_id']])): ?>
                                    <?php foreach ($foodItemsByCategory[$category['category_id']] as $item): ?>
                                        <div class="col-md-4 col-lg-3 mb-3">
                                            <div class="card food-item-card h-100" 
                                                 data-food-id="<?php echo $item['food_id']; ?>"
                                                 data-food-name="<?php echo htmlspecialchars($item['food_name']); ?>"
                                                 data-food-price="<?php echo $item['price']; ?>">
                                                <div class="card-body p-3">
                                                    <?php if ($item['image_path']): ?>
                                                        <img src="../../<?php echo htmlspecialchars($item['image_path']); ?>" 
                                                             alt="<?php echo htmlspecialchars($item['food_name']); ?>"
                                                             class="food-item-image mb-2">
                                                    <?php else: ?>
                                                        <div class="food-item-image mb-2 bg-light d-flex align-items-center justify-content-center">
                                                            <i class="fas fa-utensils fa-2x text-muted"></i>
                                                        </div>
                                                    <?php endif; ?>
                                                    <h6 class="card-title mb-1"><?php echo htmlspecialchars($item['food_name']); ?></h6>
                                                    <p class="card-text small text-muted mb-2">
                                                        <?php echo $item['description'] ? truncateText(htmlspecialchars($item['description']), 50) : __('no_description', 'Không có mô tả'); ?>
                                                    </p>
                                                    <div class="d-flex justify-content-between align-items-center">
                                                        <strong class="text-primary"><?php echo formatCurrency($item['price']); ?></strong>
                                                        <button class="btn btn-sm btn-primary add-item-btn">
                                                            <i class="fas fa-plus"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <div class="col-12">
                                        <p class="text-muted text-center py-4">
                                            <?php echo __('no_items_in_category', 'Không có món ăn nào trong danh mục này.'); ?>
                                        </p>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
            
            <!-- Order Summary Section -->
            <div class="col-lg-4">
                <div class="card order-summary">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-shopping-cart me-2"></i><?php echo __('order_summary', 'Tóm tắt đơn hàng'); ?>
                        </h5>
                    </div>
                    <div class="card-body">
                        <form id="orderForm" method="POST">
                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                            <input type="hidden" name="action" value="create_order">
                            <input type="hidden" name="order_items" id="orderItemsInput">
                            
                            <!-- Table Selection -->
                            <div class="mb-3">
                                <label for="table_id" class="form-label"><?php echo __('select_table', 'Chọn bàn'); ?> *</label>
                                <select id="table_id" name="table_id" class="form-select" required>
                                    <option value=""><?php echo __('select_table', 'Chọn bàn'); ?></option>
                                    <?php foreach ($availableTables as $table): ?>
                                        <option value="<?php echo $table['table_id']; ?>" 
                                                <?php echo ($selectedTable && $selectedTable['table_id'] == $table['table_id']) ? 'selected' : ''; ?>>
                                            <?php echo __('table', 'Bàn'); ?> <?php echo htmlspecialchars($table['table_number']); ?> 
                                            (<?php echo $table['capacity']; ?> <?php echo __('people', 'người'); ?>)
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            
                            <!-- Customer Information -->
                            <div class="mb-3">
                                <div class="form-outline">
                                    <input type="text" id="customer_name" name="customer_name" class="form-control">
                                    <label class="form-label" for="customer_name"><?php echo __('customer_name', 'Tên khách hàng'); ?></label>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <div class="form-outline">
                                    <input type="tel" id="customer_phone" name="customer_phone" class="form-control">
                                    <label class="form-label" for="customer_phone"><?php echo __('customer_phone', 'Số điện thoại'); ?></label>
                                </div>
                            </div>
                            
                            <!-- Order Items -->
                            <div class="mb-3">
                                <h6><?php echo __('order_items', 'Món đã chọn'); ?></h6>
                                <div id="orderItemsList" class="border rounded p-2" style="min-height: 100px; max-height: 300px; overflow-y: auto;">
                                    <p class="text-muted text-center m-0"><?php echo __('no_items_selected', 'Chưa chọn món nào'); ?></p>
                                </div>
                            </div>
                            
                            <!-- Order Total -->
                            <div class="mb-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <strong><?php echo __('total_amount', 'Tổng tiền'); ?>:</strong>
                                    <strong class="text-primary" id="orderTotal">0 ₫</strong>
                                </div>
                            </div>
                            
                            <!-- Notes -->
                            <div class="mb-3">
                                <div class="form-outline">
                                    <textarea id="notes" name="notes" class="form-control" rows="3"></textarea>
                                    <label class="form-label" for="notes"><?php echo __('order_notes', 'Ghi chú đơn hàng'); ?></label>
                                </div>
                            </div>
                            
                            <!-- Action Buttons -->
                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-success" id="createOrderBtn" disabled>
                                    <i class="fas fa-check me-2"></i><?php echo __('create_order', 'Tạo đơn hàng'); ?>
                                </button>
                                <button type="button" class="btn btn-outline-danger" id="clearOrderBtn">
                                    <i class="fas fa-trash me-2"></i><?php echo __('clear_order', 'Xóa đơn hàng'); ?>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- MDBootstrap JS -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/mdb-ui-kit/6.4.2/mdb.min.js"></script>
    <!-- Custom JS -->
    <script src="../../assets/js/app.js"></script>
    
    <script>
        // Order management
        let orderItems = [];
        let orderTotal = 0;
        
        // Category filtering
        document.querySelectorAll('.category-tab').forEach(tab => {
            tab.addEventListener('click', function() {
                // Update active tab
                document.querySelectorAll('.category-tab').forEach(t => t.classList.remove('active'));
                this.classList.add('active');
                
                const categoryId = this.dataset.category;
                const sections = document.querySelectorAll('.category-section');
                
                if (categoryId === 'all') {
                    sections.forEach(section => section.style.display = 'block');
                } else {
                    sections.forEach(section => {
                        section.style.display = section.dataset.category === categoryId ? 'block' : 'none';
                    });
                }
            });
        });
        
        // Add item to order
        document.querySelectorAll('.food-item-card').forEach(card => {
            card.addEventListener('click', function() {
                const foodId = parseInt(this.dataset.foodId);
                const foodName = this.dataset.foodName;
                const foodPrice = parseFloat(this.dataset.foodPrice);
                
                addItemToOrder(foodId, foodName, foodPrice);
            });
        });
        
        function addItemToOrder(foodId, foodName, foodPrice) {
            const existingItem = orderItems.find(item => item.food_id === foodId);
            
            if (existingItem) {
                existingItem.quantity += 1;
                existingItem.total = existingItem.quantity * existingItem.price;
            } else {
                orderItems.push({
                    food_id: foodId,
                    food_name: foodName,
                    price: foodPrice,
                    quantity: 1,
                    total: foodPrice
                });
            }
            
            updateOrderDisplay();
        }
        
        function removeItemFromOrder(foodId) {
            orderItems = orderItems.filter(item => item.food_id !== foodId);
            updateOrderDisplay();
        }
        
        function updateItemQuantity(foodId, newQuantity) {
            const item = orderItems.find(item => item.food_id === foodId);
            if (item) {
                if (newQuantity <= 0) {
                    removeItemFromOrder(foodId);
                } else {
                    item.quantity = newQuantity;
                    item.total = item.quantity * item.price;
                    updateOrderDisplay();
                }
            }
        }
        
        function updateOrderDisplay() {
            const orderItemsList = document.getElementById('orderItemsList');
            const orderTotalElement = document.getElementById('orderTotal');
            const createOrderBtn = document.getElementById('createOrderBtn');
            const orderItemsInput = document.getElementById('orderItemsInput');
            
            if (orderItems.length === 0) {
                orderItemsList.innerHTML = '<p class="text-muted text-center m-0"><?php echo __('no_items_selected', 'Chưa chọn món nào'); ?></p>';
                orderTotal = 0;
                createOrderBtn.disabled = true;
            } else {
                let html = '';
                orderTotal = 0;
                
                orderItems.forEach(item => {
                    orderTotal += item.total;
                    html += `
                        <div class="order-item">
                            <div>
                                <strong>${item.food_name}</strong><br>
                                <small class="text-muted">${formatCurrency(item.price)} × ${item.quantity}</small>
                            </div>
                            <div class="quantity-controls">
                                <button type="button" class="btn btn-sm btn-outline-danger quantity-btn" 
                                        onclick="updateItemQuantity(${item.food_id}, ${item.quantity - 1})">
                                    <i class="fas fa-minus"></i>
                                </button>
                                <span class="mx-2">${item.quantity}</span>
                                <button type="button" class="btn btn-sm btn-outline-success quantity-btn" 
                                        onclick="updateItemQuantity(${item.food_id}, ${item.quantity + 1})">
                                    <i class="fas fa-plus"></i>
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-danger ms-2" 
                                        onclick="removeItemFromOrder(${item.food_id})">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    `;
                });
                
                orderItemsList.innerHTML = html;
                createOrderBtn.disabled = false;
            }
            
            orderTotalElement.textContent = formatCurrency(orderTotal);
            orderItemsInput.value = JSON.stringify(orderItems);
        }
        
        // Clear order
        document.getElementById('clearOrderBtn').addEventListener('click', function() {
            if (confirm('<?php echo __('confirm_clear_order', 'Bạn có chắc chắn muốn xóa toàn bộ đơn hàng?'); ?>')) {
                orderItems = [];
                updateOrderDisplay();
            }
        });
        
        // Format currency function
        function formatCurrency(amount) {
            return new Intl.NumberFormat('vi-VN').format(amount) + ' ₫';
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            updateOrderDisplay();
        });
    </script>
</body>
</html>
