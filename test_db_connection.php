<?php
/**
 * Simple Database Connection Test
 * 
 * This script tests the basic database connection and table structure
 */

echo "<h2>Database Connection Test</h2>";

// Test 1: Basic PHP PDO availability
echo "<h3>1. PHP PDO Extension</h3>";
if (extension_loaded('pdo') && extension_loaded('pdo_mysql')) {
    echo "✅ PDO and PDO MySQL extensions are loaded<br>";
} else {
    echo "❌ PDO or PDO MySQL extension is not loaded<br>";
    echo "Please install/enable PDO MySQL extension<br>";
    exit;
}

// Test 2: Database configuration
echo "<h3>2. Database Configuration</h3>";
$host = 'localhost';
$db_name = 'restaurant_management';
$username = 'root';
$password = '';

echo "Host: $host<br>";
echo "Database: $db_name<br>";
echo "Username: $username<br>";
echo "Password: " . (empty($password) ? "(empty)" : "(set)") . "<br>";

// Test 3: Direct PDO connection
echo "<h3>3. Direct PDO Connection</h3>";
try {
    $dsn = "mysql:host=$host;dbname=$db_name;charset=utf8mb4";
    $options = [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false,
    ];
    
    $pdo = new PDO($dsn, $username, $password, $options);
    echo "✅ Direct PDO connection successful<br>";
    
    // Test database selection
    $stmt = $pdo->query("SELECT DATABASE() as current_db");
    $result = $stmt->fetch();
    echo "Current database: " . $result['current_db'] . "<br>";
    
} catch (PDOException $e) {
    echo "❌ Direct PDO connection failed: " . $e->getMessage() . "<br>";
    
    // Try to connect without database name to check if database exists
    try {
        $dsn_no_db = "mysql:host=$host;charset=utf8mb4";
        $pdo_no_db = new PDO($dsn_no_db, $username, $password, $options);
        echo "✅ MySQL server connection successful<br>";
        
        // Check if database exists
        $stmt = $pdo_no_db->prepare("SHOW DATABASES LIKE ?");
        $stmt->execute([$db_name]);
        if ($stmt->rowCount() > 0) {
            echo "✅ Database '$db_name' exists<br>";
        } else {
            echo "❌ Database '$db_name' does not exist<br>";
            echo "<p><strong>To create the database:</strong></p>";
            echo "<ol>";
            echo "<li>Open your MySQL client (phpMyAdmin, command line, etc.)</li>";
            echo "<li>Run: <code>CREATE DATABASE restaurant_management;</code></li>";
            echo "<li>Import the database.sql file</li>";
            echo "</ol>";
        }
    } catch (PDOException $e2) {
        echo "❌ MySQL server connection also failed: " . $e2->getMessage() . "<br>";
        echo "<p><strong>Possible issues:</strong></p>";
        echo "<ul>";
        echo "<li>MySQL server is not running</li>";
        echo "<li>Wrong host, username, or password</li>";
        echo "<li>MySQL port is not 3306 (default)</li>";
        echo "</ul>";
    }
    exit;
}

// Test 4: Check required tables
echo "<h3>4. Database Tables</h3>";
$required_tables = ['users', 'roles', 'categories', 'food_items', 'tables', 'orders', 'order_details', 'payments'];

foreach ($required_tables as $table) {
    try {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            echo "✅ Table '$table' exists<br>";
        } else {
            echo "❌ Table '$table' missing<br>";
        }
    } catch (PDOException $e) {
        echo "❌ Error checking table '$table': " . $e->getMessage() . "<br>";
    }
}

// Test 5: Check users table data
echo "<h3>5. Users Table Data</h3>";
try {
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
    $count = $stmt->fetch()['count'];
    echo "✅ Users table has $count records<br>";
    
    if ($count > 0) {
        $stmt = $pdo->query("SELECT user_id, username, email, role_id, status FROM users LIMIT 5");
        $users = $stmt->fetchAll();
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>ID</th><th>Username</th><th>Email</th><th>Role ID</th><th>Status</th></tr>";
        foreach ($users as $user) {
            echo "<tr>";
            echo "<td>{$user['user_id']}</td>";
            echo "<td>{$user['username']}</td>";
            echo "<td>{$user['email']}</td>";
            echo "<td>{$user['role_id']}</td>";
            echo "<td>{$user['status']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
} catch (PDOException $e) {
    echo "❌ Error accessing users table: " . $e->getMessage() . "<br>";
}

// Test 6: Check roles table
echo "<h3>6. Roles Table Data</h3>";
try {
    $stmt = $pdo->query("SELECT * FROM roles");
    $roles = $stmt->fetchAll();
    echo "✅ Roles table has " . count($roles) . " records<br>";
    
    if (count($roles) > 0) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>Role ID</th><th>Role Name</th></tr>";
        foreach ($roles as $role) {
            echo "<tr>";
            echo "<td>{$role['role_id']}</td>";
            echo "<td>{$role['role_name']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
} catch (PDOException $e) {
    echo "❌ Error accessing roles table: " . $e->getMessage() . "<br>";
}

// Test 7: Test simple query with parameters
echo "<h3>7. Parameter Binding Test</h3>";
try {
    // Test with positional parameters
    $stmt = $pdo->prepare("SELECT username FROM users WHERE username = ? LIMIT 1");
    $stmt->execute(['admin']);
    $result = $stmt->fetch();
    
    if ($result) {
        echo "✅ Positional parameter binding works<br>";
        echo "Found user: " . $result['username'] . "<br>";
    } else {
        echo "⚠️ Positional parameter binding works, but 'admin' user not found<br>";
    }
    
    // Test with named parameters
    $stmt = $pdo->prepare("SELECT username FROM users WHERE username = :username LIMIT 1");
    $stmt->execute([':username' => 'admin']);
    $result = $stmt->fetch();
    
    if ($result) {
        echo "✅ Named parameter binding works<br>";
    } else {
        echo "⚠️ Named parameter binding works, but 'admin' user not found<br>";
    }
    
} catch (PDOException $e) {
    echo "❌ Parameter binding test failed: " . $e->getMessage() . "<br>";
}

echo "<hr>";
echo "<h3>Summary</h3>";
echo "<p>If all tests above passed, your database connection is working correctly.</p>";
echo "<p><strong>Next steps:</strong></p>";
echo "<ul>";
echo "<li><a href='test_login.php'>Test Login Process</a></li>";
echo "<li><a href='setup_database.php'>Setup Database with Correct Passwords</a></li>";
echo "<li><a href='views/auth/login.php'>Try Login Page</a></li>";
echo "</ul>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
h2 { color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px; }
h3 { color: #007bff; margin-top: 30px; border-left: 4px solid #007bff; padding-left: 10px; }
table { border-collapse: collapse; margin: 10px 0; }
th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
code { background: #f8f9fa; padding: 2px 4px; border-radius: 3px; font-family: monospace; }
ul, ol { margin: 10px 0; padding-left: 30px; }
li { margin: 5px 0; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
</style>
