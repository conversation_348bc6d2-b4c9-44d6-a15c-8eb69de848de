<?php
/**
 * Login Test Script
 * 
 * This script tests the login process step by step to identify issues
 */

require_once 'config/config.php';
require_once 'config/database.php';
require_once 'includes/functions.php';
require_once 'models/User.php';

echo "<h2>Login Process Test</h2>";

// Test credentials
$testUsername = 'admin';
$testPassword = 'admin123';

echo "<p><strong>Testing login with:</strong></p>";
echo "<ul>";
echo "<li>Username: $testUsername</li>";
echo "<li>Password: $testPassword</li>";
echo "</ul>";

// Step 1: Test database connection
echo "<h3>Step 1: Database Connection</h3>";
if ($pdo) {
    echo "✅ Database connected successfully<br>";
} else {
    echo "❌ Database connection failed<br>";
    exit;
}

// Step 2: Test User model instantiation
echo "<h3>Step 2: User Model</h3>";
try {
    $userModel = new User();
    echo "✅ User model created successfully<br>";
} catch (Exception $e) {
    echo "❌ Failed to create User model: " . $e->getMessage() . "<br>";
    exit;
}

// Step 3: Test direct database query
echo "<h3>Step 3: Direct Database Query</h3>";
try {
    $sql = "SELECT u.*, r.role_name
            FROM users u
            JOIN roles r ON u.role_id = r.role_id
            WHERE (u.username = ? OR u.email = ?)
            AND u.status = 1";

    $stmt = $pdo->prepare($sql);
    $stmt->execute([$testUsername, $testUsername]);
    
    $user = $stmt->fetch();
    
    if ($user) {
        echo "✅ User found in database<br>";
        echo "<strong>User details:</strong><br>";
        echo "- User ID: {$user['user_id']}<br>";
        echo "- Username: {$user['username']}<br>";
        echo "- Email: {$user['email']}<br>";
        echo "- Full Name: {$user['full_name']}<br>";
        echo "- Role: {$user['role_name']}<br>";
        echo "- Status: {$user['status']}<br>";
        echo "- Password Hash: " . substr($user['password'], 0, 20) . "...<br>";
    } else {
        echo "❌ User not found in database<br>";
        exit;
    }
} catch (PDOException $e) {
    echo "❌ Database query failed: " . $e->getMessage() . "<br>";
    exit;
}

// Step 4: Test password verification
echo "<h3>Step 4: Password Verification</h3>";
$isPasswordValid = password_verify($testPassword, $user['password']);
echo "Password verification result: " . ($isPasswordValid ? "✅ VALID" : "❌ INVALID") . "<br>";

if (!$isPasswordValid) {
    echo "<p style='color: red;'><strong>Password verification failed!</strong></p>";
    echo "<p>This means the stored password hash doesn't match the test password.</p>";
    
    // Generate a new hash for comparison
    $newHash = password_hash($testPassword, PASSWORD_DEFAULT);
    echo "<p><strong>New hash for '$testPassword':</strong><br>";
    echo "<code>$newHash</code></p>";
    
    echo "<p><strong>Current stored hash:</strong><br>";
    echo "<code>{$user['password']}</code></p>";
    
    // Test if the new hash works
    $newHashWorks = password_verify($testPassword, $newHash);
    echo "<p>New hash verification test: " . ($newHashWorks ? "✅ WORKS" : "❌ FAILS") . "</p>";
}

// Step 5: Test User model authenticate method
echo "<h3>Step 5: User Model Authentication</h3>";
$authenticatedUser = $userModel->authenticate($testUsername, $testPassword);

if ($authenticatedUser) {
    echo "✅ User model authentication successful<br>";
    echo "<strong>Authenticated user data:</strong><br>";
    echo "<pre>" . print_r($authenticatedUser, true) . "</pre>";
} else {
    echo "❌ User model authentication failed<br>";
}

// Step 6: Test with staff credentials
echo "<h3>Step 6: Test Staff Login</h3>";
$staffUser = $userModel->authenticate('staff', 'staff123');
if ($staffUser) {
    echo "✅ Staff authentication successful<br>";
} else {
    echo "❌ Staff authentication failed<br>";
}

// Step 7: Provide fix if needed
if (!$isPasswordValid) {
    echo "<h3>Step 7: Fix Password Hashes</h3>";
    echo "<p style='color: orange;'>The password hashes in your database are incorrect.</p>";
    echo "<form method='POST'>";
    echo "<input type='hidden' name='fix_passwords' value='1'>";
    echo "<button type='submit' style='background: #dc3545; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;'>Fix Password Hashes Now</button>";
    echo "</form>";
    
    if ($_POST['fix_passwords'] ?? false) {
        echo "<h4>Fixing password hashes...</h4>";
        
        try {
            // Fix admin password
            $adminHash = password_hash('admin123', PASSWORD_DEFAULT);
            $stmt = $pdo->prepare("UPDATE users SET password = ? WHERE username = 'admin'");
            $stmt->execute([$adminHash]);
            
            // Fix staff password
            $staffHash = password_hash('staff123', PASSWORD_DEFAULT);
            $stmt = $pdo->prepare("UPDATE users SET password = ? WHERE username = 'staff'");
            $stmt->execute([$staffHash]);
            
            echo "✅ Password hashes fixed successfully!<br>";
            echo "<p style='color: green; font-weight: bold;'>You can now try logging in again.</p>";
            echo "<a href='views/auth/login.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Go to Login Page</a>";
            
        } catch (PDOException $e) {
            echo "❌ Failed to fix passwords: " . $e->getMessage() . "<br>";
        }
    }
} else {
    echo "<h3>✅ All Tests Passed!</h3>";
    echo "<p style='color: green; font-weight: bold;'>The login system should work correctly now.</p>";
    echo "<a href='views/auth/login.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Go to Login Page</a>";
}

echo "<hr>";
echo "<p><strong>Additional Tools:</strong></p>";
echo "<ul>";
echo "<li><a href='debug_auth.php'>Full Authentication Debug</a></li>";
echo "<li><a href='setup_database.php'>Database Setup</a></li>";
echo "<li><a href='generate_hashes.php'>Generate Password Hashes</a></li>";
echo "</ul>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
h2 { color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px; }
h3 { color: #007bff; margin-top: 30px; border-left: 4px solid #007bff; padding-left: 10px; }
h4 { color: #28a745; margin-top: 20px; }
code { background: #f8f9fa; padding: 2px 4px; border-radius: 3px; font-family: monospace; word-break: break-all; }
pre { background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; border-left: 4px solid #007bff; }
ul { margin: 10px 0; padding-left: 30px; }
li { margin: 5px 0; }
button { transition: background-color 0.3s; }
button:hover { opacity: 0.9; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
</style>
