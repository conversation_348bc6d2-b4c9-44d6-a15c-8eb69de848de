<?php
/**
 * Payment Processing Page
 * 
 * Payment interface with multiple payment methods (cash, card, mobile payment)
 * and receipt generation functionality.
 */

require_once '../../config/config.php';
require_once '../../config/database.php';
require_once '../../includes/functions.php';
require_once '../../models/Order.php';
require_once '../../models/Payment.php';
require_once '../../models/Table.php';

// Check staff access
requireLogin();

// Initialize models
$orderModel = new Order();
$paymentModel = new Payment();
$tableModel = new Table();

// Get order ID from URL
$orderId = (int)($_GET['order_id'] ?? 0);

if ($orderId <= 0) {
    setFlashMessage('error', __('invalid_order_id', 'ID đơn hàng không hợp lệ.'));
    header('Location: orders.php');
    exit();
}

// Get order details
$order = $orderModel->getOrderById($orderId);
if (!$order) {
    setFlashMessage('error', __('order_not_found', 'Không tìm thấy đơn hàng.'));
    header('Location: orders.php');
    exit();
}

// Check if order is completed
if ($order['status'] !== 'completed') {
    setFlashMessage('error', __('order_not_completed', 'Đơn hàng chưa hoàn thành. Vui lòng hoàn thành đơn hàng trước khi thanh toán.'));
    header("Location: order-details.php?id=$orderId");
    exit();
}

// Check if already paid
$existingPayment = $paymentModel->getPaymentByOrderId($orderId);
if ($existingPayment) {
    setFlashMessage('info', __('order_already_paid', 'Đơn hàng này đã được thanh toán.'));
    header("Location: order-details.php?id=$orderId");
    exit();
}

// Get order items and calculate totals
$orderItems = $orderModel->getOrderItems($orderId);
$subtotal = 0;
foreach ($orderItems as $item) {
    $subtotal += $item['unit_price'] * $item['quantity'];
}

// Calculate tax and service charge
$taxRate = 0.10; // 10% VAT
$serviceRate = 0.05; // 5% service charge
$taxAmount = $subtotal * $taxRate;
$serviceAmount = $subtotal * $serviceRate;
$totalAmount = $subtotal + $taxAmount + $serviceAmount;

// Handle payment processing
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
        setFlashMessage('error', __('invalid_csrf_token', 'Token bảo mật không hợp lệ. Vui lòng thử lại.'));
    } else {
        $action = $_POST['action'] ?? '';
        
        if ($action === 'process_payment') {
            $paymentData = [
                'order_id' => $orderId,
                'payment_method' => sanitizeInput($_POST['payment_method'] ?? ''),
                'amount_received' => (float)($_POST['amount_received'] ?? 0),
                'total_amount' => $totalAmount,
                'staff_id' => $_SESSION['user_id'],
                'notes' => sanitizeInput($_POST['payment_notes'] ?? '')
            ];
            
            // Validation
            $validMethods = ['cash', 'card', 'mobile', 'bank_transfer'];
            if (!in_array($paymentData['payment_method'], $validMethods)) {
                setFlashMessage('error', __('invalid_payment_method', 'Phương thức thanh toán không hợp lệ.'));
            } elseif ($paymentData['amount_received'] < $totalAmount) {
                setFlashMessage('error', __('insufficient_amount', 'Số tiền nhận không đủ để thanh toán.'));
            } else {
                // Calculate change
                $changeAmount = $paymentData['amount_received'] - $totalAmount;
                $paymentData['change_amount'] = $changeAmount;
                
                // Process payment
                $paymentId = $paymentModel->createPayment($paymentData);
                if ($paymentId) {
                    // Update table status to available
                    $tableModel->updateTableStatus($order['table_id'], 'available');
                    
                    setFlashMessage('success', __('payment_processed_success', 'Thanh toán thành công.'));
                    logActivity("Xử lý thanh toán cho đơn hàng #$orderId", $_SESSION['user_id']);
                    
                    // Redirect to receipt page
                    header("Location: receipt.php?payment_id=$paymentId");
                    exit();
                } else {
                    setFlashMessage('error', __('payment_process_failed', 'Xử lý thanh toán thất bại. Vui lòng thử lại.'));
                }
            }
        }
    }
}

$pageTitle = __('payment_processing', 'Xử lý thanh toán') . ' - ' . __('order', 'Đơn hàng') . ' #' . $orderId . ' - ' . APP_NAME;
?>
<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?></title>
    
    <!-- MDBootstrap CSS -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/mdb-ui-kit/6.4.2/mdb.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="../../assets/css/custom.css" rel="stylesheet">
    
    <style>
        .payment-method-card {
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }
        .payment-method-card:hover {
            border-color: #007bff;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .payment-method-card.selected {
            border-color: #007bff;
            background-color: #f8f9ff;
        }
        .payment-summary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
        }
        .amount-display {
            font-size: 2rem;
            font-weight: bold;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        .calculator-btn {
            width: 60px;
            height: 60px;
            font-size: 1.2rem;
            font-weight: bold;
            border-radius: 10px;
            margin: 2px;
        }
        .receipt-preview {
            background: #f8f9fa;
            border: 1px dashed #dee2e6;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="dashboard.php">
                <i class="fas fa-utensils me-2"></i><?php echo APP_NAME; ?>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-1"></i><?php echo __('dashboard', 'Bảng điều khiển'); ?>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="tables.php">
                            <i class="fas fa-chair me-1"></i><?php echo __('tables', 'Bàn'); ?>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="orders.php">
                            <i class="fas fa-receipt me-1"></i><?php echo __('orders', 'Đơn hàng'); ?>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="#">
                            <i class="fas fa-credit-card me-1"></i><?php echo __('payment', 'Thanh toán'); ?>
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle me-1"></i>
                            <?php echo htmlspecialchars($_SESSION['full_name']); ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="profile.php">
                                <i class="fas fa-user me-2"></i><?php echo __('profile', 'Hồ sơ'); ?>
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../auth/logout.php">
                                <i class="fas fa-sign-out-alt me-2"></i><?php echo __('logout', 'Đăng xuất'); ?>
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    
    <!-- Main Content -->
    <div class="container-fluid mt-4">
        <div class="row">
            <!-- Payment Form -->
            <div class="col-lg-8">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2>
                        <i class="fas fa-credit-card me-2"></i>
                        <?php echo __('payment_processing', 'Xử lý thanh toán'); ?>
                    </h2>
                    <div>
                        <a href="order-details.php?id=<?php echo $orderId; ?>" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-2"></i><?php echo __('back_to_order', 'Quay về đơn hàng'); ?>
                        </a>
                    </div>
                </div>
                
                <?php displayFlashMessage(); ?>
                
                <!-- Order Summary -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-receipt me-2"></i><?php echo __('order_summary', 'Tóm tắt đơn hàng'); ?> #<?php echo $orderId; ?>
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong><?php echo __('table', 'Bàn'); ?>:</strong> 
                                   <?php echo __('table', 'Bàn'); ?> <?php echo htmlspecialchars($order['table_number']); ?>
                                </p>
                                <p><strong><?php echo __('customer_name', 'Tên khách hàng'); ?>:</strong> 
                                   <?php echo $order['customer_name'] ? htmlspecialchars($order['customer_name']) : __('not_specified', 'Không xác định'); ?>
                                </p>
                            </div>
                            <div class="col-md-6">
                                <p><strong><?php echo __('staff', 'Nhân viên'); ?>:</strong> 
                                   <?php echo htmlspecialchars($order['staff_name']); ?>
                                </p>
                                <p><strong><?php echo __('order_time', 'Giờ đặt'); ?>:</strong> 
                                   <?php echo formatVietnameseDate($order['created_at']); ?>
                                </p>
                            </div>
                        </div>
                        
                        <!-- Order Items -->
                        <div class="table-responsive mt-3">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th><?php echo __('food_name', 'Tên món ăn'); ?></th>
                                        <th><?php echo __('quantity', 'Số lượng'); ?></th>
                                        <th><?php echo __('unit_price', 'Đơn giá'); ?></th>
                                        <th><?php echo __('total_price', 'Thành tiền'); ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($orderItems as $item): ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($item['food_name']); ?></td>
                                            <td><?php echo $item['quantity']; ?></td>
                                            <td><?php echo formatCurrency($item['unit_price']); ?></td>
                                            <td><?php echo formatCurrency($item['unit_price'] * $item['quantity']); ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                
                <!-- Payment Form -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-money-bill-wave me-2"></i><?php echo __('payment_details', 'Chi tiết thanh toán'); ?>
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" id="paymentForm">
                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                            <input type="hidden" name="action" value="process_payment">
                            
                            <!-- Payment Method Selection -->
                            <div class="mb-4">
                                <h6><?php echo __('select_payment_method', 'Chọn phương thức thanh toán'); ?></h6>
                                <div class="row">
                                    <div class="col-md-3 mb-3">
                                        <div class="card payment-method-card" data-method="cash">
                                            <div class="card-body text-center">
                                                <i class="fas fa-money-bill-alt fa-2x text-success mb-2"></i>
                                                <h6><?php echo __('payment_cash', 'Tiền mặt'); ?></h6>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <div class="card payment-method-card" data-method="card">
                                            <div class="card-body text-center">
                                                <i class="fas fa-credit-card fa-2x text-primary mb-2"></i>
                                                <h6><?php echo __('payment_card', 'Thẻ tín dụng'); ?></h6>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <div class="card payment-method-card" data-method="mobile">
                                            <div class="card-body text-center">
                                                <i class="fas fa-mobile-alt fa-2x text-warning mb-2"></i>
                                                <h6><?php echo __('payment_mobile', 'Ví điện tử'); ?></h6>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <div class="card payment-method-card" data-method="bank_transfer">
                                            <div class="card-body text-center">
                                                <i class="fas fa-university fa-2x text-info mb-2"></i>
                                                <h6><?php echo __('payment_bank', 'Chuyển khoản'); ?></h6>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <input type="hidden" name="payment_method" id="paymentMethod" required>
                            </div>
                            
                            <!-- Amount Received -->
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-outline mb-3">
                                        <input type="number" id="amount_received" name="amount_received" class="form-control" 
                                               step="1000" min="<?php echo $totalAmount; ?>" value="<?php echo $totalAmount; ?>" required>
                                        <label class="form-label" for="amount_received"><?php echo __('amount_received', 'Số tiền nhận'); ?> (VND) *</label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-outline mb-3">
                                        <input type="number" id="change_amount" class="form-control" readonly>
                                        <label class="form-label" for="change_amount"><?php echo __('change_amount', 'Tiền thối'); ?> (VND)</label>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Payment Notes -->
                            <div class="form-outline mb-3">
                                <textarea id="payment_notes" name="payment_notes" class="form-control" rows="3"></textarea>
                                <label class="form-label" for="payment_notes"><?php echo __('payment_notes', 'Ghi chú thanh toán'); ?></label>
                            </div>
                            
                            <!-- Submit Button -->
                            <div class="d-grid">
                                <button type="submit" class="btn btn-success btn-lg" id="processPaymentBtn" disabled>
                                    <i class="fas fa-check me-2"></i><?php echo __('process_payment', 'Xử lý thanh toán'); ?>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            
            <!-- Payment Summary -->
            <div class="col-lg-4">
                <div class="card payment-summary mb-4">
                    <div class="card-body text-center">
                        <h5 class="mb-3">
                            <i class="fas fa-calculator me-2"></i><?php echo __('payment_calculation', 'Tính toán thanh toán'); ?>
                        </h5>
                        
                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <span><?php echo __('subtotal', 'Tạm tính'); ?>:</span>
                                <span><?php echo formatCurrency($subtotal); ?></span>
                            </div>
                            <div class="d-flex justify-content-between">
                                <span><?php echo __('tax', 'Thuế VAT'); ?> (10%):</span>
                                <span><?php echo formatCurrency($taxAmount); ?></span>
                            </div>
                            <div class="d-flex justify-content-between">
                                <span><?php echo __('service_charge', 'Phí dịch vụ'); ?> (5%):</span>
                                <span><?php echo formatCurrency($serviceAmount); ?></span>
                            </div>
                            <hr class="my-2">
                            <div class="d-flex justify-content-between">
                                <strong><?php echo __('total_amount', 'Tổng tiền'); ?>:</strong>
                                <strong class="amount-display"><?php echo formatCurrency($totalAmount); ?></strong>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Quick Amount Buttons -->
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-hand-holding-usd me-2"></i><?php echo __('quick_amounts', 'Số tiền nhanh'); ?>
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <?php
                            $quickAmounts = [
                                $totalAmount,
                                ceil($totalAmount / 50000) * 50000,
                                ceil($totalAmount / 100000) * 100000,
                                ceil($totalAmount / 200000) * 200000,
                                ceil($totalAmount / 500000) * 500000,
                                1000000
                            ];
                            $quickAmounts = array_unique($quickAmounts);
                            sort($quickAmounts);
                            
                            foreach ($quickAmounts as $amount):
                            ?>
                                <div class="col-6 mb-2">
                                    <button type="button" class="btn btn-outline-primary w-100 quick-amount-btn" 
                                            data-amount="<?php echo $amount; ?>">
                                        <?php echo formatCurrency($amount); ?>
                                    </button>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- MDBootstrap JS -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/mdb-ui-kit/6.4.2/mdb.min.js"></script>
    <!-- Custom JS -->
    <script src="../../assets/js/app.js"></script>
    
    <script>
        const totalAmount = <?php echo $totalAmount; ?>;
        
        // Payment method selection
        document.querySelectorAll('.payment-method-card').forEach(card => {
            card.addEventListener('click', function() {
                // Remove selected class from all cards
                document.querySelectorAll('.payment-method-card').forEach(c => c.classList.remove('selected'));
                
                // Add selected class to clicked card
                this.classList.add('selected');
                
                // Set payment method value
                document.getElementById('paymentMethod').value = this.dataset.method;
                
                // Enable process button
                updateProcessButton();
            });
        });
        
        // Quick amount buttons
        document.querySelectorAll('.quick-amount-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const amount = parseFloat(this.dataset.amount);
                document.getElementById('amount_received').value = amount;
                calculateChange();
            });
        });
        
        // Amount received input
        document.getElementById('amount_received').addEventListener('input', calculateChange);
        
        function calculateChange() {
            const amountReceived = parseFloat(document.getElementById('amount_received').value) || 0;
            const changeAmount = amountReceived - totalAmount;
            document.getElementById('change_amount').value = Math.max(0, changeAmount);
            updateProcessButton();
        }
        
        function updateProcessButton() {
            const paymentMethod = document.getElementById('paymentMethod').value;
            const amountReceived = parseFloat(document.getElementById('amount_received').value) || 0;
            const processBtn = document.getElementById('processPaymentBtn');
            
            if (paymentMethod && amountReceived >= totalAmount) {
                processBtn.disabled = false;
            } else {
                processBtn.disabled = true;
            }
        }
        
        // Form submission confirmation
        document.getElementById('paymentForm').addEventListener('submit', function(e) {
            const paymentMethod = document.getElementById('paymentMethod').value;
            const amountReceived = parseFloat(document.getElementById('amount_received').value);
            const changeAmount = amountReceived - totalAmount;
            
            const methodNames = {
                'cash': '<?php echo __('payment_cash', 'Tiền mặt'); ?>',
                'card': '<?php echo __('payment_card', 'Thẻ tín dụng'); ?>',
                'mobile': '<?php echo __('payment_mobile', 'Ví điện tử'); ?>',
                'bank_transfer': '<?php echo __('payment_bank', 'Chuyển khoản'); ?>'
            };
            
            let confirmMessage = `<?php echo __('confirm_payment', 'Xác nhận thanh toán'); ?>:\n\n`;
            confirmMessage += `<?php echo __('payment_method', 'Phương thức'); ?>: ${methodNames[paymentMethod]}\n`;
            confirmMessage += `<?php echo __('total_amount', 'Tổng tiền'); ?>: ${formatCurrency(totalAmount)}\n`;
            confirmMessage += `<?php echo __('amount_received', 'Số tiền nhận'); ?>: ${formatCurrency(amountReceived)}\n`;
            if (changeAmount > 0) {
                confirmMessage += `<?php echo __('change_amount', 'Tiền thối'); ?>: ${formatCurrency(changeAmount)}\n`;
            }
            
            if (!confirm(confirmMessage)) {
                e.preventDefault();
            }
        });
        
        // Format currency function
        function formatCurrency(amount) {
            return new Intl.NumberFormat('vi-VN').format(amount) + ' ₫';
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            calculateChange();
        });
    </script>
</body>
</html>
